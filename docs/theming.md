# Theming System Guide

This document provides a comprehensive overview of theming system in the Ideal Public application, covering architecture, color palettes, styling guidelines, and implementation patterns for both light and dark modes.

## 1. Architecture Overview

### Tailwind CSS v4 Configuration
The application uses Tailwind CSS v4 with the following configuration:

- `darkMode: "class"` in `tailwind.config.js`, meaning the presence of the `dark` class on `<html>` drives variant styles
- Integration with HeroUI components through Tailwind utility classes
- Custom color palette defined in `tailwind.config.js` to ensure consistency across themes

### Redux State Management
- The Redux `ui` slice owns the `theme` state (`light | dark`), with `toggleTheme` and `setTheme` reducers (`src/store/uiSlice.ts`)
- Theme state is accessible globally through Redux selectors
- Theme changes are persisted to localStorage and synchronized across components

### Theme Observer Component
- `ThemeObserver` (`src/components/theme/ThemeObserver.tsx`) mounts once inside `Provider` and keeps the DOM, localStorage, and store in sync
- Responsible for initial theme detection and ongoing synchronization
- Ensures consistency between Redux state, DOM class, and user preferences

## 2. Color Palette & Schemes

### Primary Colors
- Primary: `blue-500` for main actions and highlights
- Primary Hover: `blue-600` for interactive states
- Primary Disabled: `blue-300` for disabled states

### Secondary Colors
- Secondary: `gray-500` for secondary actions
- Secondary Hover: `gray-600` for interactive states
- Secondary Disabled: `gray-300` for disabled states

### Semantic Colors
- Success: `green-500` for positive actions and status
- Warning: `yellow-500` for cautionary messages
- Danger: `red-500` for error states and destructive actions
- Info: `blue-400` for informational messages

### Gray Scale System
For surfaces and text elements:

**Light Mode:**
- Surface Background: `gray-50` for app shell
- Card Background: `white`
- Border: `gray-200`
- Text Primary: `gray-900`
- Text Secondary: `gray-700`
- Text Muted: `gray-500`

**Dark Mode:**
- Surface Background: `gray-900` for app shell
- Card Background: `gray-800`
- Border: `gray-700`
- Text Primary: `gray-100`
- Text Secondary: `gray-300`
- Text Muted: `gray-400`

### Accessibility Compliance
- Body text on backgrounds maintains ≥ 4.5:1 contrast ratio
- Secondary text maintains ≥ 3:1 contrast ratio
- Focus indicators use `focus:ring-primary` with appropriate dark mode variants
- Custom gradients and overlays are tested to maintain AA contrast standards

## 3. Styling Guidelines

### Tailwind Class Naming Conventions
- Use semantic naming for reusable components: `btn-primary`, `card-container`
- Follow BEM methodology for complex components: `component__element--modifier`
- Always include dark mode variants: `bg-white dark:bg-gray-800`
- Use responsive prefixes appropriately: `md:text-lg`, `lg:container`

### HeroUI Component Styling
- Combine Tailwind utilities with HeroUI color props instead of hard-coding new tokens
- Use the `classNames` API to target internal component slots without breaking default styles
- Example of proper HeroUI customization:

```tsx
<Input
  classNames={{
    inputWrapper: "bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
    label: "text-gray-700 dark:text-gray-300",
    input: "text-gray-900 dark:text-white",
    errorMessage: "text-danger dark:text-danger"
 }}
/>
```

### Surface Patterns
- App shell: `bg-gray-50 dark:bg-gray-900`
- Cards/containers: `bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-700`
- Subtle panels or overlays: `dark:bg-gray-700/30` to maintain translucency without sacrificing contrast

### Text Styling Patterns
- Headings: `text-gray-900 dark:text-gray-100`
- Body copy: `text-gray-700 dark:text-gray-300`
- Muted and helper text: `text-gray-500 dark:text-gray-400`
- Placeholders: `placeholder:text-gray-400 dark:placeholder:text-gray-500`

### Interactive Element Guidelines
- Base icons: `text-gray-400` in dark mode
- Hover states: `dark:hover:text-gray-200` for neutral, `dark:hover:text-primary`/`dark:hover:text-danger` for semantic variants
- Focus states: `focus:ring-primary` with `dark:focus:ring-primary/70` where needed
- Active states: `active:bg-gray-100 dark:active:bg-gray-700`

### Form Control Styling
- Wrapper backgrounds: `bg-gray-50 dark:bg-gray-700`
- Borders: `border-gray-200 dark:border-gray-600`
- Labels: add `dark:text-gray-300` if the default utility does not cover it
- Inputs: `bg-white dark:bg-gray-800 text-gray-900 dark:text-white`

### Responsive Design Patterns
- Use Tailwind's responsive prefixes consistently: `sm:`, `md:`, `lg:`, `xl:`, `2xl:`
- Apply mobile-first approach with progressive enhancement
- Ensure touch targets are appropriately sized for mobile devices
- Consider reduced motion preferences with `motion-safe` and `motion-reduce` variants

## 4. Dark/Light Mode Implementation

### Theme Initialization Flow
1. On boot, `ThemeObserver` runs and chooses the effective theme with this priority order:
   - Saved user preference in `localStorage.theme`
   - System preference from `window.matchMedia('(prefers-color-scheme: dark)')`
   - Default fallback (`light`)
2. The chosen value is dispatched to the Redux store if it differs from the current state
3. The `<html>` element receives or loses the `dark` class, and `html.dark { color-scheme: dark; }` in `src/styles/globals.css` updates native form controls and scrollbars
4. Every subsequent theme change (manual toggle, settings update, etc.) re-applies the class and persists the value back to `localStorage`

### Theme Toggle Components
- `DarkModeToggle` (`src/components/theme/DarkModeToggle.tsx`) presents a compact switch with Sun/Moon icons for quick access (e.g., top navigation)
- The Settings screen uses `AppearanceSection` (`src/pages/settings/components/AppearanceSection.tsx`) to render the same toggle alongside other display preferences
- Both components dispatch `toggleTheme()`, so any new consumer should import the same action
- When rendering switches, use the Redux `theme` selector for the `isSelected` state and rely on HeroUI's `onChange`/`onValueChange` signatures

### Component Patterns for Theme Toggling
```tsx
// Card container
<Card className="bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-700" />

// Icon-only button
<Button
  isIconOnly
  variant="light"
  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
>
  <MoreHorizontal />
</Button>

// Complex component with theme-aware styling
<div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
    Card Title
  </h2>
  <p className="text-gray-700 dark:text-gray-300">
    Card content with proper theming applied.
  </p>
</div>
```

### Testing Checklist for Theme Switching
- **Local preference**: Toggle the switch, refresh, and confirm the previous choice persists (inspect `localStorage.theme`)
- **System preference**: Clear the key (`localStorage.removeItem('theme')`), reload, and ensure the UI follows your OS color scheme
- **SSR safety**: New components should avoid reading `window` during render; use effects/hooks instead so dark mode loads without hydration mismatches
- **Visual sweep**: In dark mode, review app shell, cards, inputs, and any new feature specific screens for missing `dark:` variants
- **Accessibility**: Verify all text maintains proper contrast ratios in both themes
- **Focus states**: Ensure focus indicators remain visible in both themes
- **Form controls**: Check that native controls (inputs, selects, etc.) adapt properly to the theme

## 5. UI/UX Design Principles

### Spacing System
- Use Tailwind's spacing scale consistently: `space-x-2`, `p-4`, `m-6`, etc.
- Maintain consistent padding and margins across components
- Apply spacing tokens that align with your design system
- Consider responsive spacing with `md:space-x-4`, etc.

### Typography Scale
- Heading 1: `text-4xl` for main page titles
- Heading 2: `text-2xl` for section headings
- Heading 3: `text-xl` for subsections
- Body: `text-base` for main content
- Small: `text-sm` for captions and helper text
- Always maintain consistent line-heights: `leading-tight`, `leading-normal`, `leading-relaxed`

### Component Hierarchy
- Establish clear visual hierarchy with appropriate contrast and sizing
- Use consistent border-radius: `rounded-lg` for cards, `rounded-full` for circles
- Apply shadows judiciously: `shadow-sm` for subtle elevation, `shadow-lg` for prominent elements
- Maintain consistent z-index values across the application

### Interactive States
- Hover: `hover:bg-gray-100 dark:hover:bg-gray-700` for subtle interaction feedback
- Focus: `focus:outline-none focus:ring-2 focus:ring-primary` for accessibility
- Active: `active:bg-gray-200 dark:active:bg-gray-600` for pressed states
- Disabled: `opacity-50 cursor-not-allowed` for disabled elements

### Animation and Transition Guidelines
- Use consistent transition durations: `transition` (default), `transition-fast` (150ms), `transition-slow` (300ms)
- Apply easing functions appropriately: `ease-in-out` for most transitions
- Respect user preferences with `motion-safe` and `motion-reduce` variants
- Use `transform` properties for performant animations: `translate`, `scale`, `rotate`

## 6. Developer Guidelines

### Common Styling Patterns

**Button Component:**
```tsx
// Primary button
<Button 
  className="bg-primary text-white hover:bg-primary/90 dark:hover:bg-primary/80"
>
  Primary Action
</Button>

// Secondary button
<Button 
  variant="bordered"
  className="border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300"
>
  Secondary Action
</Button>
```

**Card Component:**
```tsx
<Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
  <CardBody>
    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
      Card Title
    </h3>
    <p className="text-gray-600 dark:text-gray-400">
      Card content with proper theming.
    </p>
  </CardBody>
</Card>
```

**Input Field:**
```tsx
<Input
  placeholder="Enter text here"
  className="w-full"
  classNames={{
    input: "text-gray-900 dark:text-white",
    inputWrapper: "bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
    label: "text-gray-700 dark:text-gray-300",
    errorMessage: "text-danger dark:text-danger"
  }}
/>
```

### Troubleshooting Common Issues

**Issue: Dark mode styles not applying**
- Check that the `dark` class is present on the `<html>` element
- Verify that `darkMode: "class"` is configured in `tailwind.config.js`
- Ensure your dark mode variants are properly formatted as `dark:bg-color` not `dark:bg-color-500`

**Issue: Theme flickering on page load**
- Ensure `ThemeObserver` is mounted early in the component tree
- Add the theme class to the `<html>` element during SSR if possible
- Use appropriate loading states to avoid visual inconsistencies

**Issue: Components not respecting theme changes**
- Check that components are using Redux theme selectors
- Verify that components are re-rendering when theme changes
- Ensure `ThemeObserver` is maintaining synchronization

**Issue: Accessibility contrast problems**
- Use contrast checker tools to validate color combinations
- Ensure text meets WCAG AA standards (4.5:1 for normal text, 3:1 for large text)
- Test with accessibility tools in both light and dark modes

### Performance Considerations
- Minimize the number of theme-dependent CSS classes to reduce style recalculations
- Use CSS variables for frequently changing theme values
- Avoid inline styles that conflict with theme classes
- Consider using `transform` and `opacity` for animations rather than layout-affecting properties
- Defer non-critical theme-related JavaScript to avoid blocking the main thread

### Maintenance and Update Procedures
- Update this guide whenever introducing new primitives or adjusting the palette so the rest of the team can follow the same conventions
- When changing color values, update both the Tailwind config and this documentation
- Review all components when making global theme changes to ensure consistency
- Add new component examples to this guide when introducing common patterns
- Regularly audit the color palette to remove unused or deprecated colors
- Test theme changes across all supported browsers and devices

### Best Practices Summary
- Always include dark mode variants for every color utility used
- Use semantic color names rather than literal color names
- Maintain consistent spacing and typography scales throughout the application
- Ensure all interactive elements have appropriate hover, focus, and active states
- Follow accessibility standards for contrast and focus indicators
- Use the Redux theme state for any logic that depends on the current theme
- Test theme persistence across browser sessions
- Validate that theme changes work correctly on both desktop and mobile devices

## 7. Component Design Patterns

This section describes the common component design patterns used in the Ideal Public application, including examples for buttons, cards, forms, and navigation elements.

### Buttons

The application follows a consistent button pattern with different variants for various use cases:

```tsx
// Primary button
<Button
  color="primary"
  className="bg-primary hover:bg-primary/90 dark:hover:bg-primary/80 text-white"
>
  Primary Action
</Button>

// Secondary button
<Button
  variant="bordered"
  className="border-primary text-primary dark:border-primary dark:text-primary"
>
  Secondary Action
</Button>

// Ghost button
<Button
  variant="light"
  className="text-primary hover:bg-primary/10 dark:hover:bg-primary/20"
>
  Ghost Button
</Button>

// Icon-only button
<Button
  isIconOnly
  variant="light"
  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
  aria-label="More options"
>
  <MoreHorizontalIcon />
</Button>
```

### Cards

Cards follow a consistent design pattern with appropriate theming:

```tsx
<Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
  <CardHeader className="border-b border-gray-100 dark:border-gray-700">
    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
      Card Title
    </h3>
  </CardHeader>
  <CardBody className="p-6">
    <p className="text-gray-600 dark:text-gray-400">
      Card content with proper theming.
    </p>
  </CardBody>
  <CardFooter className="border-t border-gray-100 dark:border-gray-700 flex justify-end gap-2">
    <Button variant="light">Cancel</Button>
    <Button color="primary">Submit</Button>
  </CardFooter>
</Card>
```

### Forms

Form components follow a consistent styling pattern with proper theming:

```tsx
<div className="space-y-4">
  <Input
    label="Email"
    placeholder="Enter your email"
    className="w-full"
    classNames={{
      input: "text-gray-900 dark:text-white",
      inputWrapper: "bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
      label: "text-gray-700 dark:text-gray-300",
      errorMessage: "text-danger dark:text-danger"
    }}
  />
  
  <Textarea
    label="Message"
    placeholder="Enter your message"
    className="w-full"
    classNames={{
      input: "text-gray-900 dark:text-white",
      inputWrapper: "bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
      label: "text-gray-700 dark:text-gray-300",
      errorMessage: "text-danger dark:text-danger"
    }}
  />
  
  <Select
    label="Select Option"
    className="w-full"
    classNames={{
      trigger: "bg-gray-50 border-gray-200 dark:bg-gray-700 dark:border-gray-600",
      value: "text-gray-900 dark:text-white",
      label: "text-gray-700 dark:text-gray-300",
      errorMessage: "text-danger dark:text-danger"
    }}
  >
    <SelectItem key="option1" value="option1">Option 1</SelectItem>
    <SelectItem key="option2" value="option2">Option 2</SelectItem>
    <SelectItem key="option3" value="option3">Option 3</SelectItem>
  </Select>
</div>
```

### Navigation Elements

Navigation components maintain consistent styling and behavior:

```tsx
// Sidebar navigation
<div className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 h-full p-4">
  <div className="mb-8">
    <h2 className="text-xl font-bold text-gray-900 dark:text-white">App Name</h2>
  </div>
  <div className="space-y-1">
    <Link
      to="/dashboard"
      className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
    >
      <DashboardIcon className="mr-3" />
      Dashboard
    </Link>
    <Link
      to="/chats"
      className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
    >
      <ChatIcon className="mr-3" />
      Chats
    </Link>
    <Link
      to="/contacts"
      className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
    >
      <ContactIcon className="mr-3" />
      Contacts
    </Link>
  </div>
</div>

// Top navigation bar
<header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 py-3 px-6 flex items-center justify-between">
  <div className="flex items-center">
    <Button isIconOnly variant="light" className="mr-3">
      <MenuIcon />
    </Button>
    <h1 className="text-xl font-semibold text-gray-900 dark:text-white">Page Title</h1>
  </div>
  <div className="flex items-center space-x-3">
    <DarkModeToggle />
    <Button isIconOnly variant="light">
      <BellIcon />
    </Button>
    <Avatar src="user-avatar.jpg" />
  </div>
</header>
```

## 8. Accessibility Guidelines

The Ideal Public application follows accessibility best practices to ensure usability for all users, including those with disabilities. These guidelines ensure WCAG 2.1 AA compliance.

### WCAG Compliance

The application follows WCAG 2.1 AA guidelines with the following key principles:

- **Perceivable**: Information and UI components must be presentable to users in ways they can perceive
- **Operable**: Interface components must be operable by all users
- **Understandable**: Information and UI operation must be understandable
- **Robust**: Content must be robust enough to work with various assistive technologies

### Keyboard Navigation

All interactive elements are fully accessible via keyboard:

```tsx
// Proper focus management for custom components
<div
  tabIndex={0}
  className="focus:outline-none focus:ring-2 focus:ring-primary"
  onKeyDown={(e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      // Handle action
    }
  }}
>
  Interactive element
</div>

// Button with proper keyboard handling
<Button
  variant="solid"
  color="primary"
  className="focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800"
  onKeyDown={(e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      // Handle button action
    }
  }}
>
  Accessible Button
</Button>

// Modal with proper focus trapping
const Modal = ({ isOpen, onClose }) => {
  const firstFocusRef = useRef();
  const lastFocusRef = useRef();

  useEffect(() => {
    if (isOpen) {
      // Move focus to first focusable element in modal
      firstFocusRef.current?.focus();
    }
  }, [isOpen]);

  const handleKeyDown = (e) => {
    if (e.key === 'Tab') {
      if (e.shiftKey && document.activeElement === firstFocusRef.current) {
        e.preventDefault();
        lastFocusRef.current?.focus();
      } else if (!e.shiftKey && document.activeElement === lastFocusRef.current) {
        e.preventDefault();
        firstFocusRef.current?.focus();
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
      onKeyDown={handleKeyDown}
    >
      <div
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-full max-w-md"
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
      >
        <h2 id="modal-title" className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
          Modal Title
        </h2>
        <p className="mb-6 text-gray-70 dark:text-gray-300">
          Modal content goes here.
        </p>
        <div className="flex justify-end gap-2">
          <Button
            variant="light"
            ref={firstFocusRef}
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            ref={lastFocusRef}
          >
            Confirm
          </Button>
        </div>
      </div>
    </div>
  );
};
```

### ARIA Attributes

Proper ARIA attributes are used throughout the application:

```tsx
// ARIA for form elements
<Input
  label="Username"
  placeholder="Enter your username"
  aria-describedby="username-help"
  aria-invalid={hasError}
  className="w-full"
/>
<p id="username-help" className="text-sm text-gray-500 dark:text-gray-400 mt-1">
  Your username should be unique.
</p>

// ARIA for navigation
<nav aria-label="Main navigation">
  <ul className="flex space-x-4">
    <li>
      <Link
        to="/dashboard"
        className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
        aria-current={location.pathname === '/dashboard' ? 'page' : undefined}
      >
        Dashboard
      </Link>
    </li>
    {/* Additional navigation items */}
  </ul>
</nav>

// ARIA for tabs
const [activeTab, setActiveTab] = useState(0);

<div role="tablist" className="flex border-b border-gray-200 dark:border-gray-700">
  <button
    role="tab"
    aria-selected={activeTab === 0}
    aria-controls="tab-panel-1"
    id="tab-1"
    className={`px-4 py-2 ${activeTab === 0 ? 'text-primary border-b-2 border-primary' : 'text-gray-500 hover:text-gray-700 dark:hover:text-gray-30'}`}
    onClick={() => setActiveTab(0)}
  >
    Tab 1
  </button>
  <button
    role="tab"
    aria-selected={activeTab === 1}
    aria-controls="tab-panel-2"
    id="tab-2"
    className={`px-4 py-2 ${activeTab === 1 ? 'text-primary border-b-2 border-primary' : 'text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'}`}
    onClick={() => setActiveTab(1)}
  >
    Tab 2
  </button>
</div>

<div
  role="tabpanel"
  id="tab-panel-1"
  aria-labelledby="tab-1"
  hidden={activeTab !== 0}
  className="p-4"
>
  Content for Tab 1
</div>
<div
  role="tabpanel"
  id="tab-panel-2"
  aria-labelledby="tab-2"
  hidden={activeTab !== 1}
  className="p-4"
>
  Content for Tab 2
</div>

// ARIA for loading states
<div
  className="flex items-center justify-center p-8"
  aria-live="polite"
  aria-busy={isLoading}
>
  {isLoading ? (
    <div className="flex flex-col items-center">
      <Spinner size="lg" />
      <p className="mt-2 text-gray-600 dark:text-gray-400">Loading...</p>
    </div>
  ) : (
    <div>{content}</div>
  )}
</div>

// ARIA for alerts and notifications
{showSuccess && (
  <div
    role="alert"
    aria-live="assertive"
    className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative"
  >
    <strong className="font-bold">Success! </strong>
    <span className="block sm:inline">Your action was completed successfully.</span>
  </div>
)}

// ARIA for skip links (for screen readers)
<a
  href="#main-content"
  className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:bg-white focus:text-gray-900 focus:px-4 focus:py-2 focus:rounded-lg"
>
  Skip to main content
</a>
```

## 9. Theme Customization Examples

Developers can customize the theme in various ways, from adding custom colors to modifying components and creating additional themes. Here are examples of how to implement these customizations.

### Adding Custom Colors

To add custom colors to the theme, extend the Tailwind configuration:

```js
// tailwind.config.js
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        brand: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
        // Add more custom color scales as needed
      },
    },
  },
  plugins: [],
}
```

Then use the custom colors in your components:

```tsx
// Using custom brand colors
<div className="bg-brand-500 text-white p-4 rounded-lg">
  <h2 className="font-bold">Brand Highlight</h2>
  <p>Using custom brand colors for special sections</p>
</div>

<Button className="bg-brand-500 hover:bg-brand-600 text-white">
  Brand Button
</Button>
```

### Modifying Components

To modify existing components with custom styles, you can use the `tailwind-variants` library:

```tsx
import { tv } from 'tailwind-variants';

const button = tv({
  base: 'px-4 py-2 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2',
  variants: {
    variant: {
      primary: 'bg-primary text-white hover:bg-primary/90 focus:ring-primary',
      secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600',
      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500 dark:text-gray-300 dark:hover:bg-gray-700',
    },
    size: {
      sm: 'text-sm px-2 py-1',
      md: 'text-base px-4 py-2',
      lg: 'text-lg px-6 py-3',
    },
    fullWidth: {
      true: 'w-full',
    },
  },
  defaultVariants: {
    variant: 'primary',
    size: 'md',
  },
});

// Usage
const CustomButton = ({ children, variant, size, fullWidth, className, ...props }) => {
  return (
    <button
      className={button({ variant, size, fullWidth, className })}
      {...props}
    >
      {children}
    </button>
  );
};

// Using the custom button
<CustomButton variant="primary" size="lg">
  Large Primary Button
</CustomButton>

<CustomButton variant="secondary" fullWidth>
  Full Width Secondary Button
</CustomButton>
```

### Creating Additional Themes

To create additional themes beyond light and dark, you can extend the Redux state and add theme classes:

```tsx
// In src/store/uiSlice.ts
interface UiState {
  theme: 'light' | 'dark' | 'high-contrast'; // Add additional themes
  // ... other state properties
}

const initialState: UiState = {
  theme: 'light',
  // ... other initial state
};

export const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<UiState['theme']>) => {
      state.theme = action.payload;
    },
    toggleTheme: (state) => {
      // Add logic to cycle through themes
      if (state.theme === 'light') {
        state.theme = 'dark';
      } else if (state.theme === 'dark') {
        state.theme = 'high-contrast';
      } else {
        state.theme = 'light';
      }
    },
  },
});

// In ThemeObserver component
useEffect(() => {
  // Apply theme class to html element
  document.documentElement.className = theme; // This will add the theme class to the html element
  localStorage.setItem('theme', theme);
}, [theme]);
```

Then add the CSS for the new theme:

```css
/* In src/styles/globals.css */
.high-contrast {
  --tw-color-primary: #0000;
  --tw-color-secondary: #ffffff;
  --tw-color-background: #ffffff;
  --tw-color-text: #0000;
  --tw-color-border: #0000;
}

.high-contrast .card {
  background-color: var(--tw-color-background);
  border: 2px solid var(--tw-color-border);
  color: var(--tw-color-text);
}

.high-contrast .text-primary {
  color: var(--tw-color-primary);
}
```

And use theme-specific classes in components:

```tsx
// Component with multiple theme support
const Card = ({ children, className }) => {
  return (
    <div className={`
      bg-white dark:bg-gray-800 high-contrast:bg-white
      border border-gray-200 dark:border-gray-700 high-contrast:border-black
      rounded-lg shadow-sm
      p-4
      ${className || ''}
    `}>
      {children}
    </div>
  );
};
```

## 10. Responsive Design Patterns

The Ideal Public application implements responsive design patterns to ensure optimal user experience across different screen sizes. This section outlines the breakpoint system and implementation examples.

### Breakpoint System

The application uses Tailwind's default breakpoints with the following definitions:

- `sm`: 640px (640px and above)
- `md`: 768px (768px and above)
- `lg`: 1024px (1024px and above)
- `xl`: 1280px (1280px and above)
- `2xl`: 1536px (1536px and above)

### Layout Patterns

Here are common responsive layout patterns used in the application:

```tsx
// Responsive grid layout
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
    <h3 className="font-medium text-gray-900 dark:text-white">Item 1</h3>
    <p className="text-gray-600 dark:text-gray-400">Content for item 1</p>
  </div>
  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
    <h3 className="font-medium text-gray-90 dark:text-white">Item 2</h3>
    <p className="text-gray-600 dark:text-gray-400">Content for item 2</p>
  </div>
  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
    <h3 className="font-medium text-gray-900 dark:text-white">Item 3</h3>
    <p className="text-gray-600 dark:text-gray-400">Content for item 3</p>
  </div>
  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
    <h3 className="font-medium text-gray-900 dark:text-white">Item 4</h3>
    <p className="text-gray-600 dark:text-gray-400">Content for item 4</p>
  </div>
</div>

// Responsive navigation that collapses on smaller screens
const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <span className="text-xl font-bold text-gray-900 dark:text-white">App Name</span>
            </div>
            <div className="hidden md:ml-6 md:flex md:space-x-8">
              <a href="#" className="border-primary text-gray-900 dark:text-white inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                Dashboard
              </a>
              <a href="#" className="border-transparent text-gray-500 dark:text-gray-30 hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-20 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                Team
              </a>
              <a href="#" className="border-transparent text-gray-500 dark:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-20 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                Projects
              </a>
            </div>
          </div>
          <div className="flex items-center md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              {/* Mobile menu icon */}
              <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
          <div className="hidden md:flex items-center">
            <DarkModeToggle />
            <Button isIconOnly variant="light" className="ml-4">
              <BellIcon />
            </Button>
            <div className="ml-4 relative">
              <Avatar src="user-avatar.jpg" />
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden">
          <div className="pt-2 pb-3 space-y-1">
            <a href="#" className="bg-primary/10 border-primary text-primary block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
              Dashboard
            </a>
            <a href="#" className="border-transparent text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-20 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
              Team
            </a>
            <a href="#" className="border-transparent text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-200 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
              Projects
            </a>
          </div>
          <div className="pt-4 pb-3 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center px-4">
              <div className="flex-shrink-0">
                <Avatar src="user-avatar.jpg" />
              </div>
              <div className="ml-3">
                <div className="text-base font-medium text-gray-80 dark:text-white">User Name</div>
                <div className="text-sm font-medium text-gray-500 dark:text-gray-400"><EMAIL></div>
              </div>
            </div>
            <div className="mt-3 space-y-1">
              <a href="#" className="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700">Your Profile</a>
              <a href="#" className="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700">Settings</a>
              <a href="#" className="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-10 dark:hover:bg-gray-700">Sign out</a>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

// Responsive card layout with different behavior on different screen sizes
<div className="container mx-auto px-4 py-8">
  <div className="flex flex-col lg:flex-row gap-6">
    {/* Sidebar - moves to top on mobile, left on desktop */}
    <aside className="lg:w-1/4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow h-fit lg:sticky top-4">
      <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Filters</h2>
      <div className="space-y-3">
        <Checkbox>Option 1</Checkbox>
        <Checkbox>Option 2</Checkbox>
        <Checkbox>Option 3</Checkbox>
      </div>
    </aside>

    {/* Main content area */}
    <main className="lg:w-3/4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3, 4, 5, 6].map((item) => (
          <Card key={item} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <CardBody>
              <div className="bg-gray-200 dark:bg-gray-700 border-2 border-dashed rounded-xl w-16 h-16 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Card Title {item}</h3>
              <p className="text-gray-600 dark:text-gray-400">Card content goes here. This shows how the layout adapts to different screen sizes.</p>
            </CardBody>
            <CardFooter className="flex justify-end">
              <Button variant="light">Action</Button>
              <Button color="primary" className="ml-2">Primary</Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </main>
  </div>
</div>
```

### Responsive Typography

Adjust typography for different screen sizes:

```tsx
// Responsive typography example
<div className="container mx-auto px-4 py-8">
  <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
    Responsive Heading
  </h1>
  <p className="text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-400 mb-6">
    This text will scale appropriately on different screen sizes.
  </p>
  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Section Title</h2>
      <p className="text-gray-600 dark:text-gray-400">
        Content for the first section with responsive text sizing.
      </p>
    </div>
    <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Section Title</h2>
      <p className="text-gray-600 dark:text-gray-400">
        Content for the second section with responsive text sizing.
      </p>
    </div>
  </div>
</div>
```

## 11. Animation & Transition Guidelines

The application follows consistent animation and transition patterns to enhance user experience while maintaining performance. This section outlines the guidelines and provides code examples.

### Timing Functions and Durations

The application uses consistent timing functions and durations for animations:

- Default transition: `transition` (150ms ease-in-out)
- Fast transition: `transition duration-10 ease-in-out` (100ms)
- Slow transition: `transition duration-300 ease-in-out` (300ms)
- Custom timing: `transition-[cubic-bezier(0.4, 0, 0.2, 1)]` for material design style

```tsx
// Examples of different transition speeds
<Button
  className="transition-colors duration-100 ease-in-out hover:bg-primary/90"
>
  Fast Transition Button
</Button>

<Button
  className="transition-colors duration-150 ease-in-out hover:bg-primary/90"
>
  Default Transition Button
</Button>

<Button
  className="transition-colors duration-300 ease-in-out hover:bg-primary/90"
>
  Slow Transition Button
</Button>

// Custom easing function
<div className="transition-all duration-200 ease-[cubic-bezier(0.4,0,0.2,1)] hover:scale-105">
  Element with custom easing
</div>
```

### Hover, Focus, and State Transitions

Consistent transition patterns for interactive states:

```tsx
// Button with comprehensive state transitions
<Button
  className={`
    bg-primary text-white
    transition-all duration-200 ease-in-out
    hover:bg-primary/90 hover:shadow-md
    active:scale-95 active:bg-primary/80
    focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800
  `}
>
  Interactive Button
</Button>

// Card with hover effect
<Card
  className={`
    bg-white dark:bg-gray-800
    border border-gray-200 dark:border-gray-700
    rounded-lg shadow-sm
    transition-all duration-300 ease-in-out
    hover:shadow-md hover:-translate-y-1
    overflow-hidden
  `}
>
  <CardBody>
    <h3 className="text-lg font-medium text-gray-90 dark:text-white mb-2">
      Hover Card
    </h3>
    <p className="text-gray-600 dark:text-gray-400">
      This card has a smooth hover effect with elevation and subtle movement.
    </p>
  </CardBody>
</Card>

// Input field with focus transition
<Input
  placeholder="Focus to see transition"
  className="w-full"
  classNames={{
    input: "text-gray-900 dark:text-white",
    inputWrapper: `
      bg-gray-50 border border-gray-200
      dark:bg-gray-700 dark:border-gray-600
      transition-all duration-200 ease-in-out
      focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800
      focus:border-primary
    `,
    label: "text-gray-700 dark:text-gray-300",
    errorMessage: "text-danger dark:text-danger"
  }}
/>

// Checkbox with state transition
const [isChecked, setIsChecked] = useState(false);

<div
  onClick={() => setIsChecked(!isChecked)}
  className={`
    w-6 h-6 flex items-center justify-center rounded border-2 cursor-pointer
    transition-all duration-200 ease-in-out
    ${isChecked
      ? 'bg-primary border-primary'
      : 'border-gray-300 dark:border-gray-600 hover:border-primary'}
  `}
>
  {isChecked && (
    <svg
      className="w-4 h-4 text-white"
      fill="none"
      stroke="currentColor"
      viewBox="0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M5 13l4 4L19 7"
      />
    </svg>
  )}
</div>
```

### Loading and State Animations

Loading states and transitions between states:

```tsx
// Loading spinner with animation
const LoadingSpinner = () => (
  <div className="flex justify-center items-center">
    <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
  </div>
);

// Animated content loader
const ContentLoader = () => (
  <div className="space-y-4">
    <div className="animate-pulse flex space-x-4">
      <div className="rounded-full bg-gray-300 dark:bg-gray-600 h-12 w-12"></div>
      <div className="flex-1 space-y-2">
        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-5/6"></div>
      </div>
    </div>
  </div>
);

// Fade in animation for new content
const FadeInDiv = ({ children, show = true }) => (
  <div
    className={`
      transition-opacity duration-300 ease-in-out
      ${show ? 'opacity-100' : 'opacity-0'}
    `}
  >
    {children}
  </div>
);

// Slide in animation
const SlideInDiv = ({ children, show = true }) => (
  <div
    className={`
      transition-transform duration-300 ease-in-out
      ${show ? 'translate-x-0' : '-translate-x-full'}
    `}
  >
    {children}
  </div>
);

// Scale animation
const ScaleInDiv = ({ children, show = true }) => (
  <div
    className={`
      transition-transform duration-200 ease-in-out
      ${show ? 'scale-100' : 'scale-95'}
    `}
  >
    {children}
  </div>
);
```

### Modal and Overlay Animations

Smooth animations for modals and overlays:

```tsx
// Animated modal component
const AnimatedModal = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 animate-fade-in"
      onClick={onClose}
    >
      <div
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-full max-w-md animate-scale-in"
        onClick={(e) => e.stopPropagation()}
        role="dialog"
        aria-modal="true"
      >
        {children}
      </div>
    </div>
  );
};

// Custom animation styles (to be added to CSS)
// In your CSS file:
/*
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out forwards;
}

.animate-fade-out {
  animation: fadeOut 0.2s ease-in forwards;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out forwards;
}

.animate-scale-out {
  animation: scaleOut 0.2s ease-in forwards;
}
*/

// Slide in panel (like sidebar)
const SlideInPanel = ({ isOpen, onClose, children, position = 'left' }) => {
  const positionClass = position === 'left'
    ? 'left-0 -translate-x-full'
    : position === 'right'
      ? 'right-0 translate-x-full'
      : 'left-0 -translate-x-full';

  const openClass = position === 'left'
    ? 'translate-x-0'
    : position === 'right'
      ? 'translate-x-0'
      : 'translate-x-0';

  return (
    <div
      className={`
        fixed top-0 bottom-0 z-50 w-80 bg-white dark:bg-gray-800 shadow-xl
        transition-transform duration-300 ease-in-out
        ${positionClass}
        ${isOpen ? openClass : ''}
      `}
    >
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Panel Title</h2>
        <Button isIconOnly variant="light" onClick={onClose}>
          <XIcon />
        </Button>
      </div>
      <div className="p-4 overflow-y-auto h-[calc(100%-60px)]">
        {children}
      </div>
    </div>
  );
};

// Respect user's reduced motion preferences
const MotionAwareComponent = () => {
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

  return (
    <div
      className={`
        ${prefersReducedMotion
          ? 'transition-none'
          : 'transition-all duration-300 ease-in-out hover:scale-105'}
      `}
    >
      <p>Content that respects reduced motion preferences</p>
    </div>
  );
};

// Using Tailwind's motion-safe and motion-reduce variants
<div className="motion-safe:transition-all motion-safe:duration-300 motion-reduce:transition-none">
  <p>This element will animate only if the user doesn't prefer reduced motion</p>
</div>
```