# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Environment files
.env
.env.local
.env.development
.env.development.local
.env.test
.env.test.local
.env.production
.env.production.local

# Build outputs
/build
/coverage
*.tsbuildinfo

# IDE and editor files
!.vscode/extensions.json
.idea
.qoder
.figma
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
Thumbs.db

# Temporary files
*.tmp
*.temp

# Vite related
.vite
*.env.*

# Testing
/coverage

# Dependencies
pnpm-lock.yaml
yarn.lock
package-lock.json
bun.lockb