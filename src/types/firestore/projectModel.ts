import dayjs from "dayjs";
import { FirestoreDataConverter, QueryDocumentSnapshot, Timestamp } from "firebase/firestore";

import { toTimestamp } from "@/utils/firestoreUtils";

/**
 * Interface for the nested WhatsApp credentials object
 */
export interface WhatsappCredentials {
  phoneNumber: string;
  provider: string;
  bearerToken: string;
  whatsappBusinessAccountId: string;
  phoneNumberId: string;
  webhookVerificationToken: string;
}

export interface ProjectUserAccess {
  uid: string;
  email: string;
  role: string;
}

/**
 * Interface for application data (with `uid` as document ID)
 */
export interface ProjectModel {
  uid: string;
  projectName: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  whatsappCredentials: WhatsappCredentials;
  ownerUserUid: string;
  userAccess: {
    uids: string[];
    users: ProjectUserAccess[];
  };
}

/**
 * Interface for raw Firestore data (without `uid`)
 */
export interface RawProjectModel<DATE = Timestamp> {
  projectName: string;
  description: string;
  createdAt: DATE;
  updatedAt: DATE;
  whatsappCredentials: WhatsappCredentials;
  ownerUserUid: string;
  userAccess: {
    uids: string[];
    users: ProjectUserAccess[];
  };
}

/**
 * Firestore converter for Project documents
 * Handles transformation between app data and Firestore data
 */
export class ProjectModelFirestore {
  static converter(): FirestoreDataConverter<
    ProjectModel,
    RawProjectModel<Timestamp>
  > {
    return {
      toFirestore(model: ProjectModel): RawProjectModel<Timestamp> {
        // Create a copy without the uid field
        const { createdAt, updatedAt, ...rest } = model;

        // Convert date values to Timestamps
        return {
          ...rest,
          createdAt: toTimestamp(createdAt),
          updatedAt: toTimestamp(updatedAt),
        };
      },

      fromFirestore(snapshot: QueryDocumentSnapshot): ProjectModel {
        const data = snapshot.data() as RawProjectModel<Timestamp>;

        // Convert Timestamps to Dates and add the uid field
        return {
          uid: snapshot.id,
          projectName: data.projectName,
          description: data.description,
          createdAt: dayjs(data.createdAt.toDate()).toISOString(),
          updatedAt: dayjs(data.updatedAt.toDate()).toISOString(),
          whatsappCredentials: data.whatsappCredentials,
          ownerUserUid: data.ownerUserUid,
          userAccess: data.userAccess,
        };
      },
    };
  }
}
