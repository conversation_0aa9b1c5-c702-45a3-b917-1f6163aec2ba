import dayjs from "dayjs";
import {
  FirestoreDataConverter,
  QueryDocumentSnapshot,
  Timestamp,
} from "firebase/firestore";

import {
  MessageModel,
  MessageModelFirestore,
  RawMessageModel,
} from "@/types/firestore/messageModel";
import { toTimestamp } from "@/utils/firestoreUtils";

/**
 * Interface for application data (with `id` as document ID)
 */
export interface ChatModel {
  id: string;
  phoneNumber: string;
  name: string;
  label: string | null;
  createdAt: string;
  updatedAt: string;
  lastMessage: MessageModel<string> | null;
}

/**
 * Interface for raw Firestore data (without `id`)
 */
export interface RawChatModel<DATE = Timestamp> {
  phoneNumber: string;
  name: string;
  label: string | null;
  createdAt: DATE;
  updatedAt: DATE;
  lastMessage: RawMessageModel<DATE> | null;
}

/**
 * Firestore converter for Chat documents
 * Handles transformation between app data and Firestore data
 */
export class ChatModelFirestore {
  static converter(): FirestoreDataConverter<
    ChatModel,
    RawChatModel<Timestamp>
  > {
    return {
      toFirestore(chat: ChatModel): RawChatModel<Timestamp> {
        // Note: We don't include the id in the Firestore document as it's the document ID
        return {
          phoneNumber: chat.phoneNumber,
          name: chat.name,
          label: chat.label,
          createdAt: toTimestamp(chat.createdAt),
          updatedAt: toTimestamp(chat.updatedAt),
          lastMessage: chat.lastMessage
            ? MessageModelFirestore.toRaw(chat.lastMessage)
            : null,
        };
      },

      fromFirestore(snapshot: QueryDocumentSnapshot): ChatModel {
        const data = snapshot.data() as RawChatModel<Timestamp>;

        const rawLastMessage = data.lastMessage;

        const lastMessage: MessageModel<string> | null = rawLastMessage
          ? MessageModelFirestore.fromRaw(rawLastMessage)
          : null;

        return {
          id: snapshot.id,
          phoneNumber: data.phoneNumber,
          name: data.name,
          label: data.label,
          createdAt: dayjs(data.createdAt.toDate()).toISOString(),
          updatedAt: dayjs(data.updatedAt.toDate()).toISOString(),
          lastMessage,
        };
      },
    };
  }
}
