import dayjs from "dayjs";
import {
  FirestoreDataConverter,
  QueryDocumentSnapshot,
  Timestamp,
} from "firebase/firestore";

import { toTimestamp } from "@/utils/firestoreUtils";

// Type for message types
export type TMessageType =
  | "text"
  | "image"
  | "audio"
  | "video"
  | "document"
  | "sticker"
  | "location"
  | "contacts"
  | "reaction"
  | "interactive"
  | "button"
  | "order"
  | "system"
  | "unsupported";

// Message context interface
export interface MessageContext {
  forwarded: boolean;
  frequently_forwarded: boolean;
  from: string | null;
  id: string | null;
  referred_product?: Record<string, unknown>;
}

// Text message interface
export interface TextMessage {
  text?: {
    body: string;
  };
}

// Reaction message interface
export interface ReactionMessage {
  reaction?: {
    message_id: string;
    emoji: string;
  };
}

// Media message detail interface
export interface MediaMessageDetail {
  id: string;
  mime_type: string;
  sha256: string;
  caption?: string;
}

// Media message interface
export interface MediaMessage {
  image?: MediaMessageDetail;
  audio?: MediaMessageDetail;
  video?: MediaMessageDetail;
  document?: MediaMessageDetail;
  sticker?: MediaMessageDetail;
}

// Location message interface
export interface LocationMessage {
  location?: {
    latitude: string;
    longitude: string;
    name?: string;
    address?: string;
  };
}

// Contacts message interface
export interface ContactsMessage {
  contacts?: {
    emails: {
      email: string;
      type: string;
    }[];
    name: {
      formatted_name: string;
      first_name: string;
      last_name: string;
      middle_name: string;
      suffix: string;
      prefix: string;
    };
    phones: {
      phone: string;
      type: string;
      wa_id: string;
    }[];
    urls: {
      url: string;
      type: string;
    }[];
  }[];
}

// Quick reply button message interface
export interface QuickReplyButtonMessage {
  button?: {
    payload: string;
    text: string;
  };
}

// Interactive list message and quick reply button message interface
export interface InteractiveListMessageAndQuickReplyButtonMessage {
  interactive?: {
    type: "list_reply" | "button_reply";
    list_reply?: {
      id: string;
      title: string;
      description?: string;
    };
    button_reply?: {
      id: string;
      title: string;
    };
  };
}

// WhatsApp ad message interface
export interface WhatsappAdMessage {
  referral?: {
    source_type: string;
    source_id?: string;
    source_url?: string;
    body?: string;
    headline?: string;
    media_type?: string;
    image_url?: string;
    video_url?: string;
    thumbnail_url?: string;
    ctwa_clid?: string;
  };
}

// Statuses interface with generic DATE parameter
export interface Statuses<DATE = string> {
  latest: "sent" | "delivered" | "read" | "failed";
  timestamp: DATE;
  details: {
    sent: DATE | null;
    delivered: DATE | null;
    read: DATE | null;
    failed: DATE | null;
  } | null;
}

// Message interface
export interface Message<DATE = string>
  extends TextMessage,
    ReactionMessage,
    MediaMessage,
    LocationMessage,
    ContactsMessage,
    QuickReplyButtonMessage,
    InteractiveListMessageAndQuickReplyButtonMessage,
    WhatsappAdMessage {
  id: string;
  from: string;
  timestamp: DATE;
  type: TMessageType;
  context?: MessageContext | null;
}

// MessageModel interface with createdAt as ISO string
export interface MessageModel<DATE = string> {
  message: Message<DATE>;
  statuses: Statuses<DATE> | null;
  direction: "in" | "out";
  createdAt: DATE;
  sentBy?: {
    uid: string;
    name: string;
    email: string;
  };
}

// RawMessageModel interface with createdAt as Timestamp
export interface RawMessageModel<DATE = Timestamp> {
  message: Message<DATE>;
  statuses: Statuses<DATE> | null;
  direction: "in" | "out";
  createdAt: DATE;
  sentBy?: {
    uid: string;
    name: string;
    email: string;
  };
}

/**
 * Firestore converter for Message documents
 * Handles transformation between app data and Firestore data
 */
export class MessageModelFirestore {
  static converter(): FirestoreDataConverter<
    MessageModel<string>,
    RawMessageModel<Timestamp>
  > {
    return {
      toFirestore: (model: MessageModel<string>) =>
        MessageModelFirestore.toRaw(model),
      fromFirestore: (snapshot: QueryDocumentSnapshot) =>
        MessageModelFirestore.fromSnapshot(snapshot),
    };
  }

  /**
   * Convert app data to Firestore raw data
   */
  static toRaw(model: MessageModel<string>): RawMessageModel<Timestamp> {
    const { message, statuses, createdAt, direction, sentBy } = model;

    const { timestamp: messageTimestamp, ...messageRest } = message;

    return {
      message: {
        ...messageRest,
        timestamp: toTimestamp(messageTimestamp),
      },
      statuses: statuses
        ? {
            latest: statuses.latest,
            timestamp: toTimestamp(statuses.timestamp),
            details: statuses.details
              ? {
                  sent: statuses.details.sent
                    ? toTimestamp(statuses.details.sent)
                    : null,
                  delivered: statuses.details.delivered
                    ? toTimestamp(statuses.details.delivered)
                    : null,
                  read: statuses.details.read
                    ? toTimestamp(statuses.details.read)
                    : null,
                  failed: statuses.details.failed
                    ? toTimestamp(statuses.details.failed)
                    : null,
                }
              : null,
          }
        : null,
      direction,
      createdAt: toTimestamp(createdAt),
      ...(sentBy ? { sentBy } : {}),
    };
  }

  /**
   * Convert Firestore snapshot to app data
   */
  static fromSnapshot(snapshot: QueryDocumentSnapshot): MessageModel<string> {
    const raw = snapshot.data() as RawMessageModel<Timestamp>;

    return MessageModelFirestore.fromRaw(raw);
  }

  /**
   * Convert raw Firestore data to app data
   */
  static fromRaw(raw: RawMessageModel<Timestamp>): MessageModel<string> {
    const { message, statuses, direction, createdAt, sentBy } = raw;
    const { timestamp: messageTimestamp, ...messageRest } = message;

    return {
      message: {
        ...messageRest,
        timestamp: dayjs(messageTimestamp.toDate()).toISOString(),
      },
      statuses: statuses
        ? {
            latest: statuses.latest,
            timestamp: dayjs(statuses.timestamp.toDate()).toISOString(),
            details: statuses.details
              ? {
                  sent: statuses.details.sent
                    ? dayjs(statuses.details.sent.toDate()).toISOString()
                    : null,
                  delivered: statuses.details.delivered
                    ? dayjs(statuses.details.delivered.toDate()).toISOString()
                    : null,
                  read: statuses.details.read
                    ? dayjs(statuses.details.read.toDate()).toISOString()
                    : null,
                  failed: statuses.details.failed
                    ? dayjs(statuses.details.failed.toDate()).toISOString()
                    : null,
                }
              : null,
          }
        : null,
      direction,
      createdAt: dayjs(createdAt.toDate()).toISOString(),
      ...(sentBy ? { sentBy } : {}),
    };
  }
}
