// Application-level types for normalized chat data
export type MessageContentType =
  | "text"
  | "image"
  | "audio"
  | "video"
  | "document"
  | "sticker"
  | "location"
  | "contact"
  | "button_reply"
  | "list_reply"
  | "reaction"
  | "interactive"
  | "button"
  | "order"
  | "system"
  | "unsupported";

export type MessageStatus =
  | "sending"
  | "sent"
  | "delivered"
  | "read"
  | "failed";

// Message content types based on WhatsApp message structure
export interface TextContent {
  body: string;
}

export interface MediaContent {
  id: string;
  mimeType: string;
  sha256?: string; // Made optional for backward compatibility
  caption?: string;
  filename?: string;
  size?: number;
  url?: string;
  thumbnailUrl?: string;
}

export interface LocationContent {
  latitude: string;
  longitude: string;
  name?: string;
  address?: string;
}

export interface ContactContent {
  name: {
    formattedName: string;
    firstName: string;
    lastName?: string;
    middleName?: string;
    suffix?: string;
    prefix?: string;
  };
  phones: {
    phone: string;
    type: string;
    waId?: string;
  }[];
  emails?: {
    email: string;
    type: string;
  }[];
  urls?: {
    url: string;
    type: string;
  }[];
  addresses?: {
    city?: string;
    country?: string;
    state?: string;
    street?: string;
    type: "HOME" | "WORK";
  }[];
  organization?: {
    company?: string;
    department?: string;
    title?: string;
  };
}

export interface ReactionContent {
  messageId: string;
  emoji: string;
}

export interface ButtonReplyContent {
  payload: string;
  text: string;
}

export interface ListReplyContent {
  id: string;
  title: string;
  description?: string;
}

export interface InteractiveContent {
  type: "list_reply" | "button_reply";
  listReply?: ListReplyContent;
  buttonReply?: ButtonReplyContent;
}

export interface OrderContent {
  catalogId?: string;
  productItems?: {
    productId: string;
    quantity: number;
    price?: number;
  }[];
  text?: string;
}

export interface ReferralContent {
  sourceType: string;
  sourceId?: string;
  sourceUrl?: string;
  body?: string;
  headline?: string;
  mediaType?: string;
  imageUrl?: string;
  videoUrl?: string;
  thumbnailUrl?: string;
  ctwaClid?: string;
}

// Detailed status tracking
export interface MessageStatusDetails {
  latest: "sent" | "delivered" | "read" | "failed";
  timestamp: string;
  details: {
    sent: string | null;
    delivered: string | null;
    read: string | null;
    failed: string | null;
  } | null;
}

// Simplified last message structure for UI display
export interface LastMessage {
  content: string;
  timestamp: string; // ISO format string
  sender: "user" | "contact";
  type: MessageContentType;
  whatsappMessageId?: string;
}

// Contact Profile (extended with WhatsApp data)
export interface ContactProfile {
  id: string;
  name: string;
  phoneNumber: string;
  whatsappId: string; // wa_id from WhatsApp
  email?: string;
  avatar?: string;
  lastSeen?: string; // ISO format string
  about?: string;
  isBlocked: boolean;
  isMuted: boolean;
  labels: string[];

  // WhatsApp specific data
  profile?: {
    name: string;
  };
  businessProfile?: {
    displayPhoneNumber: string;
    phoneNumberId: string;
  };

  // Extended contact information (from contact messages)
  contactInfo?: {
    addresses?: {
      city?: string;
      country?: string;
      state?: string;
      street?: string;
      type?: "HOME" | "WORK";
    }[];
    emails?: {
      email: string;
      type?: "HOME" | "WORK";
    }[];
    organization?: {
      company?: string;
      department?: string;
      title?: string;
    };
    urls?: {
      url: string;
      type?: "HOME" | "WORK";
    }[];
  };
}

// Normalized Message Structure for Application Use
export interface NormalizedMessage {
  id: string;
  chatId: string; // Derived from sender's phone number
  whatsappMessageId: string; // Original WhatsApp message ID

  // Legacy field for backward compatibility
  content: string;

  // Type-specific content (new structure)
  text?: TextContent;
  image?: MediaContent;
  audio?: MediaContent;
  video?: MediaContent;
  document?: MediaContent;
  sticker?: MediaContent;
  location?: LocationContent;
  contact?: ContactContent;
  reaction?: ReactionContent;
  interactive?: InteractiveContent;
  order?: OrderContent;

  type: MessageContentType;
  sender: {
    id: string;
    name: string;
    phoneNumber: string;
    whatsappId: string;
    type: "user" | "contact";
  };
  timestamp: string; // ISO format string

  // Simple status for backward compatibility
  status: MessageStatus;

  // Detailed status tracking (new structure)
  statusDetails?: MessageStatusDetails;

  replyTo?: {
    messageId: string;
    content: string;
    sender: string;
  };
  reactions?: MessageReaction[];
  attachments?: MessageAttachment[];
  metadata?: {
    forwarded?: boolean;
    frequentlyForwarded?: boolean;
    isFromAd?: boolean;
    referralData?: ReferralContent;
  };

  // Direction tracking
  direction: "in" | "out";
}

export interface MessageReaction {
  emoji: string;
  userId: string;
  timestamp: string; // ISO format string
}

export interface MessageAttachment {
  id: string;
  type: "image" | "audio" | "video" | "document";
  filename?: string;
  mimeType: string;
  sha256?: string; // Made optional for backward compatibility
  size?: number;
  url?: string; // Retrieved from WhatsApp Media API
  thumbnailUrl?: string;
  caption?: string;
}

// Filter and Search Types
export interface FilterCriteria {
  searchQuery: string;
  filterBy: "name" | "phone" | "label" | "all";
  sortBy: "recent" | "alphabetical";
  selectedLabels: string[];
}

// UI Component Props Types
export interface MenuItemConfig {
  id: string;
  label: string;
  icon: React.ComponentType;
  href?: string;
  onClick?: () => void;
  badge?: number;
  isActive?: boolean;
  hidden?: boolean;
}

export interface MainMenuProps {
  isExpanded: boolean;
  onToggle: () => void;
  menuItems: MenuItemConfig[];
  user: UserProfile;
}

export interface UserProfile {
  id: string;
  name: string;
  avatar?: string;
  phoneNumber: string;
}

// Menu States and Behavior
export enum MenuState {
  COLLAPSED = "collapsed", // 64px width, icons only
  EXPANDED = "expanded", // 240px width, icons + labels
  MOBILE_HIDDEN = "hidden", // Hidden on mobile
  MOBILE_OVERLAY = "overlay", // Full overlay on mobile
}

export interface MenuTransitions {
  duration: string;
  easing: string;
  properties: string[];
}

export interface MenuResponsiveBehavior {
  mobile: {
    state: MenuState;
    toggleTrigger: string;
    overlayOnExpand: boolean;
    backdrop: boolean;
  };
  tablet: {
    state: MenuState;
    expandTrigger: string;
    overlayOnExpand: boolean;
    backdrop: boolean;
  };
  desktop: {
    state: MenuState;
    expandTrigger: string;
    overlayOnExpand: boolean;
    backdrop: boolean;
  };
}

// Screen Size Types
export type ScreenSize = "mobile" | "tablet" | "desktop";

// Component Layout Props
export interface ChatLayoutProps {
  children?: React.ReactNode;
}

export interface LeftPanelProps {
  className?: string;
}

export interface CenterPanelProps {
  className?: string;
}

export interface RightPanelProps {
  className?: string;
}
