import React from "react";
import { MessageCircle, Users, TrendingUp, BarChart3 } from "lucide-react";

interface StatisticsSummaryProps {
  className?: string;
}

interface StatCard {
  id: string;
  label: string;
  value: number;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  trend?: string;
}

const StatisticsSummary: React.FC<StatisticsSummaryProps> = ({
  className = "",
}) => {
  const statCards: StatCard[] = [
    {
      id: "1",
      label: "Total Chats",
      value: 0,
      icon: MessageCircle,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      trend: "+0%",
    },
    {
      id: "3",
      label: "Total Contacts",
      value: 0,
      icon: Users,
      color: "text-green-600",
      bgColor: "bg-green-50",
      trend: "+0",
    },
    {
      id: "4",
      label: "Today Messages",
      value: 0,
      icon: TrendingUp,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      trend: "+0",
    },
  ];

  const formatNumber = (num: number): string => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + "k";
    }

    return num.toString();
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className} dark:bg-gray-800 dark:border-gray-700`}
    >
      {/* Header */}
      <div className="flex items-center gap-2 p-4 border-b border-gray-100 dark:border-gray-700">
        <BarChart3 className="w-5 h-5 text-indigo-600" />
        <h3 className="font-semibold text-gray-900 dark:text-gray-100">
          Activity Summary
        </h3>
      </div>

      {/* Stats Grid */}
      <div className="p-4">
        <div className="grid grid-cols-2 gap-4">
          {statCards.map((stat) => {
            const IconComponent = stat.icon;

            return (
              <div
                key={stat.id}
                className="relative overflow-hidden rounded-lg border border-gray-100 p-4 hover:shadow-md transition-shadow duration-20 dark:border-gray-700 dark:hover:bg-gray-700/30"
              >
                {/* Background Pattern */}
                <div
                  className={`absolute top-0 right-0 w-16 h-16 ${stat.bgColor} rounded-full -mr-8 -mt-8 opacity-20 dark:opacity-10`}
                />

                {/* Content */}
                <div className="relative">
                  <div className="flex items-center justify-between mb-2">
                    <div
                      className={`p-2 rounded-lg ${stat.bgColor} dark:bg-opacity-30`}
                    >
                      <IconComponent
                        className={`w-4 h-4 ${stat.color} dark:text-opacity-80`}
                      />
                    </div>
                    {stat.trend && (
                      <span className="text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full dark:bg-green-900/30 dark:text-green-300">
                        {stat.trend}
                      </span>
                    )}
                  </div>

                  <div className="space-y-1">
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {formatNumber(stat.value)}
                    </p>
                    <p className="text-sm text-gray-600 font-medium dark:text-gray-300">
                      {stat.label}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Footer dengan insight */}
      <div className="px-4 pb-4">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-100 dark:from-blue-900/30 dark:to-indigo-900/30 dark:border-blue-900/50">
          <div className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            <p className="text-sm text-blue-800 font-medium dark:text-blue-200">
              You&apos;re more active today!
            </p>
          </div>
          <p className="text-xs text-blue-600 mt-1 ml-6 dark:text-blue-400">
            0 messages sent compared to yesterday
          </p>
        </div>
      </div>
    </div>
  );
};

export default StatisticsSummary;
