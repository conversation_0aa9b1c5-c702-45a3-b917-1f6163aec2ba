import { useState, useEffect } from "react";
import { Button, Input, Link } from "@heroui/react";
import { Eye, EyeOff, Mail, Lock, User } from "lucide-react";

import { useAuth } from "@/components/providers/AuthProvider.tsx";
import { signUp } from "@/services/main/registerMainService.ts";
import AuthFooter from "@/components/auth/AuthFooter.tsx";

interface FormErrors {
  fullName?: string;
  email?: string;
  password?: string;
}

export default function SignUp() {
  const auth = useAuth();
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [generalError, setGeneralError] = useState<string | null>(null);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setFormErrors({});
    setGeneralError(null);

    try {
      // Register the user
      await signUp(email, password, name);

      // Auto-login after successful registration
      await auth.login(email, password);
    } catch (err: any) {
      setLoading(false);

      // Parse error response
      if (
        err.response?.data?.errors &&
        Array.isArray(err.response.data.errors)
      ) {
        // Handle validation errors
        const errors: FormErrors = {};

        err.response.data.errors.forEach((error: any) => {
          const field = error.path?.[0];

          if (field && typeof field === "string") {
            if (errors[field as keyof FormErrors]) {
              errors[field as keyof FormErrors] += "; " + error.message;
            } else {
              errors[field as keyof FormErrors] = error.message;
            }
          }
        });
        setFormErrors(errors);
      }

      if (err.response?.data?.message) {
        setGeneralError(err.response.data.message);
        // Special handling for duplicate email
        if (err.response.data.message.includes("already in use")) {
          setFormErrors((prev) => ({
            ...prev,
            email: err.response.data.message,
          }));
        }
      } else if (!err.response?.data?.errors) {
        // Fallback error message
        setGeneralError("Failed to create account. Please try again.");
      }

      return;
    }
  };

  useEffect(() => {
    if (auth.state === "loggedIn") {
      setTimeout(() => {
        window.location.href = "/";
      }, 1000);
    }
  }, [auth.state]);

  const inputClassNames = {
    input:
      "text-gray-700 placeholder:text-gray-400 dark:text-gray-100 dark:placeholder:text-gray-500",
    inputWrapper:
      "bg-white/80 backdrop-blur-sm border border-gray-200 hover:border-blue-300 focus-within:border-blue-500 transition-all duration-200 dark:bg-gray-800/80 dark:border-gray-600 dark:hover:border-blue-400 dark:focus-within:border-blue-400",
    label: "text-gray-600 dark:text-gray-300",
  };

  return (
    <div className="min-h-screen flex bg-gradient-to-br from-purple-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Left side - Background Pattern */}
      <div className="hidden lg:flex lg:w-2/3 relative bg-gradient-to-br from-purple-50 via-purple-100 to-blue-100 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800">
        {/* Subtle Texture Pattern */}
        <div
          className="absolute inset-0 opacity-30 dark:opacity-20"
          style={{
            backgroundImage: `
              radial-gradient(circle at 25% 75%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 75% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              linear-gradient(135deg, transparent 49%, rgba(147, 51, 234, 0.05) 50%, transparent 51%),
              linear-gradient(-135deg, transparent 49%, rgba(59, 130, 246, 0.05) 50%, transparent 51%)
            `,
            backgroundSize: "60px 60px, 80px 80px, 20px 20px, 20px 20px",
            backgroundPosition: "0 0, 40px 40px, 0 0, 10px 10px",
          }}
        />

        {/* Enhanced Overlay for Better Text Contrast */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/60 via-purple-500/40 to-blue-600/60 dark:from-purple-900/80 dark:via-purple-800/60 dark:to-blue-900/80" />

        {/* Content Overlay */}
        <div className="absolute inset-0 flex items-center justify-center p-12">
          <div className="text-center max-w-md">
            <h2 className="text-4xl font-bold mb-4 text-white drop-shadow-2xl">
              Join Our Team
            </h2>
            <p className="text-lg text-white/95 drop-shadow-lg font-medium">
              Create your account and start your journey with Ideal Lumatera
            </p>
          </div>
        </div>
      </div>

      {/* Right side - SignUp Form */}
      <div className="w-full lg:w-1/3 flex flex-col justify-center p-8 lg:p-12 bg-white/80 backdrop-blur-sm dark:bg-gray-900/80">
        {/* Main Content Container */}
        <div className="flex flex-col space-y-8 max-w-md mx-auto w-full">
          {/* Header with Logo */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-2xl">IL</span>
              </div>
            </div>
            <div className="space-y-2">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Ideal Lumatera
              </h2>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                Create Account
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Join us and start your journey today
              </p>
            </div>
          </div>

          {/* SignUp Form */}
          <div className="space-y-6">
            {generalError && (
              <div
                className="rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-sm text-red-600 dark:border-red-500/40 dark:bg-red-950 dark:text-red-200"
                role="alert"
              >
                {generalError}
              </div>
            )}
            {auth.errorMessages && (
              <div
                className="rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-sm text-red-600 dark:border-red-500/40 dark:bg-red-950 dark:text-red-200"
                role="alert"
              >
                {auth.errorMessages}
              </div>
            )}
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-5">
                <div>
                  <Input
                    required
                    aria-describedby={
                      formErrors.fullName ? "fullName-error" : undefined
                    }
                    classNames={inputClassNames}
                    disabled={loading}
                    isInvalid={!!formErrors.fullName}
                    label="Full Name"
                    labelPlacement="inside"
                    placeholder="Enter your full name"
                    size="lg"
                    startContent={
                      <User className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                    }
                    type="text"
                    value={name}
                    variant="bordered"
                    onChange={(e) => {
                      setName(e.target.value);
                      // Clear field-level error when user starts typing
                      if (formErrors.fullName) {
                        setFormErrors((prev) => {
                          const newErrors = { ...prev };

                          delete newErrors.fullName;

                          return newErrors;
                        });
                      }
                      // Clear general error when user starts typing
                      if (generalError) {
                        setGeneralError(null);
                      }
                    }}
                  />
                  {formErrors.fullName && (
                    <div
                      className="text-red-500 text-sm mt-1"
                      id="fullName-error"
                      role="alert"
                    >
                      {formErrors.fullName}
                    </div>
                  )}
                </div>

                <div>
                  <Input
                    required
                    aria-describedby={
                      formErrors.email ? "email-error" : undefined
                    }
                    classNames={inputClassNames}
                    disabled={loading}
                    isInvalid={!!formErrors.email}
                    label="Email"
                    labelPlacement="inside"
                    placeholder="Enter your email address"
                    size="lg"
                    startContent={
                      <Mail className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                    }
                    type="email"
                    value={email}
                    variant="bordered"
                    onChange={(e) => {
                      setEmail(e.target.value);
                      // Clear field-level error when user starts typing
                      if (formErrors.email) {
                        setFormErrors((prev) => {
                          const newErrors = { ...prev };

                          delete newErrors.email;

                          return newErrors;
                        });
                      }
                      // Clear general error when user starts typing, especially for email errors
                      if (generalError) {
                        setGeneralError(null);
                      }
                    }}
                  />
                  {formErrors.email && (
                    <div
                      className="text-red-500 text-sm mt-1"
                      id="email-error"
                      role="alert"
                    >
                      {formErrors.email}
                    </div>
                  )}
                </div>

                <div>
                  <Input
                    required
                    aria-describedby={
                      formErrors.password ? "password-error" : undefined
                    }
                    classNames={inputClassNames}
                    disabled={loading}
                    endContent={
                      <button
                        className="focus:outline-none"
                        type="button"
                        onClick={() => setIsPasswordVisible(!isPasswordVisible)}
                      >
                        {isPasswordVisible ? (
                          <EyeOff className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                        ) : (
                          <Eye className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                        )}
                      </button>
                    }
                    isInvalid={!!formErrors.password}
                    label="Password"
                    labelPlacement="inside"
                    placeholder="Create a strong password"
                    size="lg"
                    startContent={
                      <Lock className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                    }
                    type={isPasswordVisible ? "text" : "password"}
                    value={password}
                    variant="bordered"
                    onChange={(e) => {
                      setPassword(e.target.value);
                      // Clear field-level error when user starts typing
                      if (formErrors.password) {
                        setFormErrors((prev) => {
                          const newErrors = { ...prev };

                          delete newErrors.password;

                          return newErrors;
                        });
                      }
                      // Clear general error when user starts typing
                      if (generalError) {
                        setGeneralError(null);
                      }
                    }}
                  />
                  {formErrors.password && (
                    <div
                      className="text-red-500 text-sm mt-1"
                      id="password-error"
                      role="alert"
                    >
                      {formErrors.password}
                    </div>
                  )}
                </div>
              </div>

              <Button
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 font-semibold text-white shadow-lg hover:shadow-xl transition-all duration-200 dark:from-purple-500 dark:to-blue-500 dark:hover:from-purple-600 dark:hover:to-blue-600"
                disabled={loading}
                isLoading={loading}
                size="lg"
                type="submit"
              >
                {loading ? "Creating account..." : "Create Account"}
              </Button>
            </form>

            <div className="text-center">
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Already have an account?{" "}
              </span>
              <Link
                className="text-blue-600 hover:text-blue-700 font-medium dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                href="/login"
                size="sm"
              >
                Sign in
              </Link>
            </div>
          </div>

          {/* Footer */}
          <AuthFooter className="mt-8" />
        </div>
      </div>
    </div>
  );
}
