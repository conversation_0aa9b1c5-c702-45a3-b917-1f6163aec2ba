import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, Card, CardBody, Spinner } from "@heroui/react";
import { Grid3X3, List, Plus, Users } from "lucide-react";

import SearchBar from "./components/SearchBar";
import ContactList from "./components/ContactList";
import ContactModal from "./components/ContactModal";

import { RootState } from "@/store";
import { openModal, setContacts, setSearchQuery } from "@/store/contactsSlice";
import { Contact, ContactFormData } from "@/types/contact";
import { useAppSelector } from "@/store/hooks";
import DefaultLayout from "@/layouts/default.tsx";

const Contacts: React.FC = () => {
  const dispatch = useDispatch();
  const {
    filteredContacts,
    searchQuery,
    isModalOpen,
    modalMode,
    selectedContact,
    loading,
  } = useSelector((state: RootState) => state.contacts);
  const { visiblePanels, screenSize } = useAppSelector((state) => state.ui);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Calculate left margin - always maintain ml-14 when sidebar is visible on non-mobile screens
  const leftMargin =
    visiblePanels.mainMenu && screenSize !== "mobile" ? "ml-14" : "ml-0";

  useEffect(() => {
    dispatch(setContacts([]));
  }, [dispatch]);

  const handleAddContact = () => {
    dispatch(openModal({ mode: "add" }));
  };

  const handleContactSelect = (contact: Contact) => {
    dispatch(openModal({ mode: "view", contact }));
  };

  const handleSearch = (query: string) => {
    dispatch(setSearchQuery(query));
  };

  const handleSaveContact = (contactData: ContactFormData) => {
    // This will be handled by the modal component
    console.log("Saving contact:", contactData);
  };

  if (loading) {
    return (
      <div
        className={`flex items-center justify-center min-h-screen ${leftMargin} transition-all duration-200 ease-out`}
      >
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <DefaultLayout>
      <div
        className={`flex-1 overflow-y-auto ${leftMargin} transition-all duration-200 ease-out`}
      >
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div className="flex items-center gap-3">
              <Users className="w-8 h-8 text-primary" />
              <div>
                <h1 className="text-3xl font-bold text-foreground">Contacts</h1>
                <p className="text-gray-600 mt-1 dark:text-gray-400">
                  Manage your contacts ({filteredContacts.length} total)
                </p>
              </div>
            </div>
            <Button
              className="w-full sm:w-auto"
              color="primary"
              startContent={<Plus className="w-4 h-4" />}
              onPress={handleAddContact}
            >
              Add Contact
            </Button>
          </div>

          {/* Search and View Controls */}
          <Card className="mb-6 bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
            <CardBody className="p-4">
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                <div className="flex-1 w-full sm:max-w-md">
                  <SearchBar
                    placeholder="Search contacts by name, email, or phone..."
                    value={searchQuery}
                    onChange={handleSearch}
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    isIconOnly
                    aria-label="Grid view"
                    color={viewMode === "grid" ? "primary" : "default"}
                    variant={viewMode === "grid" ? "solid" : "bordered"}
                    onPress={() => setViewMode("grid")}
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </Button>
                  <Button
                    isIconOnly
                    aria-label="List view"
                    color={viewMode === "list" ? "primary" : "default"}
                    variant={viewMode === "list" ? "solid" : "bordered"}
                    onPress={() => setViewMode("list")}
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Contacts List */}
          {filteredContacts.length === 0 ? (
            <Card className="bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
              <CardBody className="text-center py-12">
                <Users className="w-16 h-16 text-gray-300 mx-auto mb-4 dark:text-gray-400" />
                <h3 className="text-xl font-semibold text-gray-800 mb-2 dark:text-gray-100">
                  {searchQuery ? "No contacts found" : "No contacts yet"}
                </h3>
                <p className="text-gray-500 mb-6 dark:text-gray-400">
                  {searchQuery
                    ? "Try adjusting your search terms"
                    : "Get started by adding your first contact"}
                </p>
                {!searchQuery && (
                  <Button color="primary" onPress={handleAddContact}>
                    Add Your First Contact
                  </Button>
                )}
              </CardBody>
            </Card>
          ) : (
            <ContactList
              contacts={filteredContacts}
              viewMode={viewMode}
              onContactSelect={handleContactSelect}
            />
          )}

          {/* Contact Modal */}
          <ContactModal
            contact={selectedContact || undefined}
            isOpen={isModalOpen}
            mode={modalMode}
            onClose={() => dispatch({ type: "contacts/closeModal" })}
            onSave={handleSaveContact}
          />
        </div>
      </div>
    </DefaultLayout>
  );
};

export default Contacts;
