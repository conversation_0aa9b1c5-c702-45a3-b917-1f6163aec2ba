import React from "react";

import { getProjectInitials } from "@/utils/projectUtils";

interface ProjectLogoProps {
  name: string;
}

const ProjectLogo: React.FC<ProjectLogoProps> = ({ name }) => {
  const initials = getProjectInitials(name);

  return (
    <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-600">
      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-purple-500 text-white font-medium text-base">
        {initials}
      </div>
    </div>
  );
};

export default ProjectLogo;
