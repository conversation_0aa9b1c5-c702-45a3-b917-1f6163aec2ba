import { Key, LogOut, Trash2, Shield } from "lucide-react";
import { But<PERSON> } from "@heroui/react";
import { useNavigate } from "react-router-dom";

interface SectionProps {
  className?: string;
}

export default function AccountSection({ className = "" }: SectionProps) {
  const navigate = useNavigate();

  const handleChangePassword = () => {
    // TODO: Implement change password functionality
    console.log("Change password clicked");
  };

  const handleLogout = () => {
    // TODO: Implement logout functionality
    console.log("Logout clicked");
    // For now, redirect to login page
    navigate("/login");
  };

  const handleDeleteAccount = () => {
    // TODO: Implement delete account functionality
    const confirmed = window.confirm(
      "Are you sure you want to delete your account? This action cannot be undone.",
    );

    if (confirmed) {
      console.log("Delete account confirmed");
    }
  };

  const handlePrivacySettings = () => {
    // TODO: Implement privacy settings
    console.log("Privacy settings clicked");
  };

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm ${className}`}
    >
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Account
      </h2>

      <div className="space-y-4">
        {/* Change Password */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Key className="w-5 h-5 text-blue-500" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                Change Password
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Update your account password
              </p>
            </div>
          </div>
          <Button
            className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
            size="sm"
            variant="bordered"
            onClick={handleChangePassword}
          >
            Change
          </Button>
        </div>

        {/* Privacy Settings */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Shield className="w-5 h-5 text-green-500" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                Privacy Settings
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Manage your privacy preferences
              </p>
            </div>
          </div>
          <Button
            className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
            size="sm"
            variant="bordered"
            onClick={handlePrivacySettings}
          >
            Manage
          </Button>
        </div>

        {/* Logout */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <LogOut className="w-5 h-5 text-orange-500" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                Logout
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Sign out of your account
              </p>
            </div>
          </div>
          <Button
            color="warning"
            size="sm"
            startContent={<LogOut className="w-4 h-4" />}
            variant="bordered"
            onClick={handleLogout}
          >
            Logout
          </Button>
        </div>

        {/* Delete Account */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <Trash2 className="w-5 h-5 text-red-500" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                Delete Account
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Permanently delete your account and all data
              </p>
            </div>
          </div>
          <Button
            className="text-red-600 hover:text-red-700"
            color="danger"
            size="sm"
            variant="light"
            onClick={handleDeleteAccount}
          >
            Delete
          </Button>
        </div>
      </div>
    </div>
  );
}
