import { Edit3 } from "lucide-react";
import { Button } from "@heroui/react";
import { useState } from "react";

import ProfileEditModal from "./ProfileEditModal.tsx";

import { useAuth } from "@/components/providers/AuthProvider.tsx";
import { getInitials as getInitialsUtil } from "@/utils/stringUtils.ts";

interface SectionProps {
  className?: string;
}

const getInitials = (name: string): string => {
  const result = getInitialsUtil(name);
  return result || "UN"; // Default fallback if result is empty
};

export default function ProfileSection({ className = "" }: SectionProps) {
  const auth = useAuth();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [userName, setUserName] = useState(auth.user?.name || "");

  const handleEditProfile = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSaveProfile = (name: string) => {
    setUserName(name);
  };

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm ${className}`}
    >
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Profile
      </h2>

      <div className="flex items-center space-x-4">
        {/* Avatar */}
        <div
          aria-label={`Avatar inisial ${getInitials(userName || auth.user?.name || "")}`}
          className="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center text-xl font-semibold text-gray-700 dark:text-gray-300"
          title={`Avatar inisial ${getInitials(userName || auth.user?.name || "")}`}
        >
          {getInitials(userName || auth.user?.name || "")}
        </div>

        {/* User Info */}
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {userName}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {auth.user?.email}
          </p>
        </div>

        {/* Edit Button */}
        <Button
          className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
          size="sm"
          startContent={<Edit3 className="w-4 h-4" />}
          variant="bordered"
          onClick={handleEditProfile}
        >
          Edit Profil
        </Button>
      </div>

      <ProfileEditModal
        initialName={userName}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleSaveProfile}
      />
    </div>
  );
}
