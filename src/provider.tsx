import { useHref, useNavigate } from "react-router-dom";
import { HeroUIProvider } from "@heroui/system";
import { Provider as ReduxProvider } from "react-redux";
import React from "react";

import { store } from "./store";

import ThemeObserver from "@/components/theme/ThemeObserver";

export function Provider({ children }: { children: React.ReactNode }) {
  const navigate = useNavigate();

  return (
    <ReduxProvider store={store}>
      <HeroUIProvider navigate={navigate} useHref={useHref}>
        <ThemeObserver />
        {children}
      </HeroUIProvider>
    </ReduxProvider>
  );
}
