import type { NormalizedMessage, MessageContentType } from "@/types";
import type { ChatModel } from "@/types/firestore/chatModel";

import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface MessageState {
  messages: Record<string, NormalizedMessage[]>;
  activeChat: ChatModel | null;
  isLoading: boolean;
}

const initialState: MessageState = {
  messages: {},
  activeChat: null,
  isLoading: false,
};

export const messageSlice = createSlice({
  name: "message",
  initialState,
  reducers: {
    setActiveChat: (state, action: PayloadAction<ChatModel>) => {
      state.activeChat = action.payload;
      // Mark messages as read when chat is opened
      const messages = state.messages[action.payload.id];

      if (messages) {
        messages.forEach((message) => {
          if (message.sender.type === "contact" && message.status !== "read") {
            message.status = "read";
          }
        });
      }
    },

    addMessage: (state, action: PayloadAction<NormalizedMessage>) => {
      const message = action.payload;

      if (!state.messages[message.chatId]) {
        state.messages[message.chatId] = [];
      }
      state.messages[message.chatId].push(message);
    },

    sendMessage: (
      state,
      action: PayloadAction<{
        chatId: string;
        content: string;
        type: MessageContentType;
      }>,
    ) => {
      const { chatId, content, type } = action.payload;
      const message: NormalizedMessage = {
        id: crypto.randomUUID(),
        chatId,
        whatsappMessageId: `temp_${Date.now()}`,
        content,
        type,

        // Add type-specific content based on message type
        ...(type === "text" && { text: { body: content } }),

        sender: {
          id: "current-user",
          name: "You",
          phoneNumber: "",
          whatsappId: "",
          type: "user",
        },
        timestamp: new Date().toISOString(),
        status: "sending",

        // Required fields
        direction: "out",
        metadata: {},
      };

      if (!state.messages[chatId]) {
        state.messages[chatId] = [];
      }
      state.messages[chatId].push(message);
    },

    updateMessageStatus: (
      state,
      action: PayloadAction<{
        messageId: string;
        status: "sent" | "delivered" | "read" | "failed";
      }>,
    ) => {
      const { messageId, status } = action.payload;

      Object.values(state.messages).forEach((chatMessages) => {
        const message = chatMessages.find((m) => m.id === messageId);

        if (message) {
          message.status = status;
        }
      });
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    setMessages: (
      state,
      action: PayloadAction<Record<string, NormalizedMessage[]>>,
    ) => {
      state.messages = action.payload;
    },
  },
});

export const {
  setActiveChat,
  addMessage,
  sendMessage,
  updateMessageStatus,
  setLoading,
  setMessages,
} = messageSlice.actions;

export default messageSlice.reducer;
