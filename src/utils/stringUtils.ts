/**
 * Extracts initials from a name string
 * - If name is empty, returns empty string
 * - If name has only one word, returns first two characters
 * - If name has multiple words, returns first character of first two words
 * - Result is always converted to uppercase
 * 
 * @param name - The name string to extract initials from
 * @returns The initials as uppercase string
 */
export const getInitials = (name: string): string => {
  if (!name) return "";
  
  // Split the name by spaces and get the first character of each word
  const nameParts = name.trim().split(/\s+/);
  
  if (nameParts.length === 1) {
    // If there's only one part, return the first two characters
    return nameParts[0].substring(0, 2).toUpperCase();
  } else {
    // If there are multiple parts, return the first character of the first two parts
    return (nameParts[0][0] + (nameParts[1][0] || "")).toUpperCase();
  }
};