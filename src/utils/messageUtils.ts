import dayjs from "dayjs";

import {
  MessageModel,
  Message,
  TMessageType,
} from "@/types/firestore/messageModel";
import {
  LastMessage,
  MessageContentType,
  NormalizedMessage,
} from "@/types/chat";

/**
 * Map Firestore TMessageType to application MessageContentType
 * @param type The Firestore message type
 * @returns The application message content type
 */
const mapMessageType = (message: Message<string>): MessageContentType => {
  switch (message.type) {
    case "text":
      return "text";
    case "image":
      return "image";
    case "audio":
      return "audio";
    case "video":
      return "video";
    case "document":
      return "document";
    case "sticker":
      return "sticker";
    case "location":
      return "location";
    case "contacts":
      return "contact";
    case "button":
      return "button_reply";
    case "interactive":
      if (message.interactive?.type === "list_reply") {
        return "list_reply";
      }
      if (message.interactive?.type === "button_reply") {
        return "button_reply";
      }

      return "text";
    case "reaction":
      return "reaction";
    case "system":
      return "system";
    case "order":
      return "document";
    case "unsupported":
    default:
      return "text";
  }
};

/**
 * Map application MessageContentType to Firestore TMessageType
 * @param type The application message content type
 * @returns The Firestore message type
 */
const mapContentType = (type: MessageContentType): TMessageType => {
  switch (type) {
    case "text":
      return "text";
    case "image":
      return "image";
    case "audio":
      return "audio";
    case "video":
      return "video";
    case "document":
      return "document";
    case "location":
      return "location";
    case "contact":
      return "contacts";
    case "button_reply":
      return "button";
    case "list_reply":
      return "interactive";
    case "reaction":
      return "reaction";
    case "system":
      return "system";
    default:
      return "unsupported";
  }
};

/**
 * Transform a MessageModel to the simplified structure used in ChatModel.lastMessage
 * @param message The MessageModel to transform
 * @returns The simplified lastMessage structure
 */
export const transformMessageModelToLastMessage = (
  model: MessageModel | null,
): LastMessage => {
  // If message is null, return a default lastMessage
  if (!model) {
    return {
      content: "No messages yet",
      timestamp: dayjs().toDate().toISOString(),
      sender: "user",
      type: "text",
      whatsappMessageId: undefined,
    };
  }

  const message = model.message;

  // Extract content based on message type
  let content = "";

  if (message.type === "text" && message.text?.body) {
    content = message.text.body;
  } else if (message.type === "image") {
    content = "📷 Image";
  } else if (message.type === "audio") {
    content = "🎵 Audio";
  } else if (message.type === "video") {
    content = "🎥 Video";
  } else if (message.type === "document") {
    content = "📄 Document";
  } else if (message.type === "sticker") {
    content = "😀 Sticker";
  } else if (message.type === "location" && message.location) {
    content = "📍 Location";
  } else if (message.type === "contacts") {
    content = "👤 Contact";
  } else if (message.type === "button" && message.button) {
    content = message.button.text;
  } else if (message.type === "interactive" && message.interactive) {
    if (
      message.interactive.type === "list_reply" &&
      message.interactive.list_reply
    ) {
      content = message.interactive.list_reply.title;
    } else if (
      message.interactive.type === "button_reply" &&
      message.interactive.button_reply
    ) {
      content = message.interactive.button_reply.title;
    } else {
      content = `[${message.type}]`;
    }
  } else if (message.type === "reaction" && message.reaction) {
    content = message.reaction.emoji;
  } else if (message.type === "system") {
    content = "System message";
  } else if (message.type === "order") {
    content = "🧾 Order";
  } else if (message.type === "unsupported") {
    content = "Unsupported message";
  } else {
    content = `[${message.type}]`;
  }

  // Determine sender type
  const sender: "user" | "contact" =
    model.direction === "out" ? "user" : "contact";

  return {
    content,
    timestamp: dayjs(message.timestamp).toDate().toISOString(),
    sender,
    type: mapMessageType(message),
    whatsappMessageId: message.id,
  };
};

/**
 * Transform a simplified lastMessage structure to a partial MessageModel
 * This is useful for creating temporary messages in the UI
 * @param lastMessage The simplified lastMessage structure
 * @param chatId The chat ID
 * @returns A partial MessageModel
 */
export const transformLastMessageToPartialMessageModel = (
  lastMessage: LastMessage,
  chatId: string,
): Partial<MessageModel> => {
  const now = dayjs();
  const direction = lastMessage.sender === "user" ? "out" : "in";
  const messageType = mapContentType(lastMessage.type);

  return {
    message: {
      id: lastMessage.whatsappMessageId || `temp_${Date.now()}`,
      from: direction === "out" ? "user" : chatId,
      timestamp: now.toISOString(),
      type: messageType,
      context: null,
      ...(messageType === "text"
        ? { text: { body: lastMessage.content } }
        : {}),
      ...(messageType === "button"
        ? { button: { text: lastMessage.content, payload: "" } }
        : {}),
      ...(messageType === "interactive"
        ? {
            interactive: {
              type:
                lastMessage.type === "list_reply"
                  ? "list_reply"
                  : "button_reply",
              ...(lastMessage.type === "list_reply"
                ? {
                    list_reply: {
                      id: "temp_option",
                      title: lastMessage.content,
                    },
                  }
                : {
                    button_reply: {
                      id: "temp_button",
                      title: lastMessage.content,
                    },
                  }),
            },
          }
        : {}),
    },
    statuses: null,
    direction,
    createdAt: now.toISOString(),
  };
};

/**
 * Transform a MessageModel to a NormalizedMessage
 * @param message The MessageModel to transform
 * @param chatId The chat ID
 * @returns The NormalizedMessage
 */
export const transformMessageModelToNormalizedMessage = (
  model: MessageModel,
  chatId: string,
): NormalizedMessage => {
  const message = model.message;
  // Extract content based on message type
  let content = "";

  if (message.type === "text" && message.text?.body) {
    content = message.text.body;
  } else if (message.type === "image") {
    content = "📷 Image";
  } else if (message.type === "audio") {
    content = "🎵 Audio";
  } else if (message.type === "video") {
    content = "🎥 Video";
  } else if (message.type === "document") {
    content = "📄 Document";
  } else if (message.type === "location" && message.location) {
    content = "📍 Location";
  } else if (message.type === "contacts") {
    content = "👤 Contact";
  } else if (message.type === "button" && message.button) {
    content = message.button.text;
  } else if (message.type === "interactive" && message.interactive) {
    if (
      message.interactive.type === "list_reply" &&
      message.interactive.list_reply
    ) {
      content = message.interactive.list_reply.title;
    } else if (
      message.interactive.type === "button_reply" &&
      message.interactive.button_reply
    ) {
      content = message.interactive.button_reply.title;
    } else {
      content = `[${message.type}]`;
    }
  } else if (message.type === "reaction" && message.reaction) {
    content = message.reaction.emoji;
  } else if (message.type === "system") {
    content = "System message";
  } else if (message.type === "order") {
    content = "🧾 Order";
  } else if (message.type === "unsupported") {
    content = "Unsupported message";
  } else {
    content = `[${message.type}]`;
  }

  // Extract sender information from the message model
  const sender = {
    id: model.direction === "out" ? "current-user" : message.from,
    name: model.direction === "out" ? "You" : message.from,
    phoneNumber: message.from || "",
    whatsappId: message.from || "",
    type: (model.direction === "out" ? "user" : "contact") as
      | "user"
      | "contact",
  };

  return {
    id: message.id,
    chatId,
    whatsappMessageId: message.id,
    content,
    type: mapMessageType(message),
    sender,
    timestamp: dayjs(message.timestamp).toDate().toISOString(),
    status: "sent",

    // Add type-specific content based on message type
    ...(message.text && { text: { body: message.text.body } }),
    ...(message.image && {
      image: {
        id: message.image.id,
        mimeType: message.image.mime_type,
        ...(message.image.sha256 && { sha256: message.image.sha256 }),
        caption: message.image.caption,
      },
    }),
    ...(message.video && {
      video: {
        id: message.video.id,
        mimeType: message.video.mime_type,
        ...(message.video.sha256 && { sha256: message.video.sha256 }),
        caption: message.video.caption,
      },
    }),
    ...(message.audio && {
      audio: {
        id: message.audio.id,
        mimeType: message.audio.mime_type,
        ...(message.audio.sha256 && { sha256: message.audio.sha256 }),
      },
    }),
    ...(message.document && {
      document: {
        id: message.document.id,
        mimeType: message.document.mime_type,
        ...(message.document.sha256 && { sha256: message.document.sha256 }),
        caption: message.document.caption,
        // filename is not available in Firestore model, will be populated elsewhere
      },
    }),
    ...(message.sticker && {
      sticker: {
        id: message.sticker.id,
        mimeType: message.sticker.mime_type,
        ...(message.sticker.sha256 && { sha256: message.sticker.sha256 }),
      },
    }),
    ...(message.location && {
      location: {
        latitude: message.location.latitude,
        longitude: message.location.longitude,
        name: message.location.name,
        address: message.location.address,
      },
    }),
    ...(message.contacts && {
      contact: message.contacts.map((contact) => ({
        name: {
          formattedName: contact.name.formatted_name,
          firstName: contact.name.first_name,
          lastName: contact.name.last_name,
          middleName: contact.name.middle_name,
          suffix: contact.name.suffix,
          prefix: contact.name.prefix,
        },
        phones: contact.phones.map((phone) => ({
          phone: phone.phone,
          type: phone.type,
          waId: phone.wa_id,
        })),
        emails: contact.emails?.map((email) => ({
          email: email.email,
          type: email.type,
        })),
        urls: contact.urls?.map((url) => ({
          url: url.url,
          type: url.type,
        })),
      }))[0],
    }),
    ...(message.reaction && {
      reaction: {
        messageId: message.reaction.message_id,
        emoji: message.reaction.emoji,
      },
    }),
    ...(message.interactive && {
      interactive: {
        type: message.interactive.type,
        ...(message.interactive.list_reply && {
          listReply: {
            id: message.interactive.list_reply.id,
            title: message.interactive.list_reply.title,
            description: message.interactive.list_reply.description,
          },
        }),
        ...(message.interactive.button_reply && {
          buttonReply: {
            payload: message.interactive.button_reply.id,
            text: message.interactive.button_reply.title,
          },
        }),
      },
    }),
    ...(message.button && {
      interactive: {
        type: "button_reply",
        buttonReply: {
          payload: message.button.payload,
          text: message.button.text,
        },
      },
    }),

    // Required fields
    direction: model.direction,
    metadata: {
      ...(message.context && {
        forwarded: message.context.forwarded,
        frequentlyForwarded: message.context.frequently_forwarded,
        isFromAd: !!message.referral,
      }),
      ...(message.referral && {
        referralData: {
          sourceType: message.referral.source_type,
          sourceId: message.referral.source_id,
          sourceUrl: message.referral.source_url,
          body: message.referral.body,
          headline: message.referral.headline,
          mediaType: message.referral.media_type,
          imageUrl: message.referral.image_url,
          videoUrl: message.referral.video_url,
          thumbnailUrl: message.referral.thumbnail_url,
          ctwaClid: message.referral.ctwa_clid,
        },
      }),
    },
  };
};
