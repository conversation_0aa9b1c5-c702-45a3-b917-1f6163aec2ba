/**
 * Extracts initials from a project name string
 * - If name is empty, returns "PR" as default
 * - If name has only one word, returns first character
 * - If name has multiple words, returns first character of first two words
 * - Result is always converted to uppercase
 *
 * @param name - The project name string to extract initials from
 * @returns The project initials as uppercase string
 */
export const getProjectInitials = (name: string): string => {
  const projectName = name || "Unnamed Project";
  const initials = projectName
    .split(" ")
    .filter(Boolean)
    .map((word) => word[0]?.toUpperCase())
    .join("");

  if (initials.length >= 2) {
    return initials.slice(0, 2);
  }

  if (initials.length === 1) {
    return initials;
  }

  return "PR";
};
