import {
  collection,
  onSnapshot,
  orderBy,
  query,
  Unsubscribe,
} from "firebase/firestore";

import { firestore } from "@/services/firebase/firebase.ts";
import { ChatModelFirestore } from "@/types/firestore/chatModel.ts";
import { ChatModel } from "@/types/firestore/chatModel.ts";

const subscribeToChats = (
  projectId: string,
  callback: (chats: ChatModel[]) => void,
  onError?: (error: Error) => void,
): Unsubscribe => {
  const chatsQuery = query(
    collection(firestore, "projects", projectId, "chats").withConverter(
      ChatModelFirestore.converter(),
    ),
    orderBy("lastMessage.message.timestamp", "desc"),
  );

  return onSnapshot(
    chatsQuery,
    (snapshot) => {
      const chats = snapshot.docs.map((doc) => {
        const data = doc.data();

        // The ChatModelFirestore converter already normalizes the data
        // So we can directly use the converted data
        return data;
      });

      callback(chats);
    },
    (error) => {
      console.error("Error listening to chats:", error);
      onError?.(error);
    },
  );
};

export default subscribeToChats;
