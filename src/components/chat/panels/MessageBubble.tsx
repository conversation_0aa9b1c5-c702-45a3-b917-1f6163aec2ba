import type { NormalizedMessage } from "../../../types";

import React, { memo } from "react";
import dayjs from "dayjs";
import {
  RiCheckDoubleFill,
  RiCheckFill,
  RiTimeFill,
  RiCloseCircleFill,
} from "react-icons/ri";

import { ChatModel } from "@/types/firestore/chatModel.ts";
import { getInitials } from "../../../utils/stringUtils.ts";

interface MessageBubbleProps {
  message: NormalizedMessage;
  isFirstInGroup: boolean;
  isLastInGroup: boolean;
  activeChat: ChatModel;
}

// Message status icons
const MessageStatusIcon = ({
  status,
}: {
  status: NormalizedMessage["status"];
}) => {
  const statusConfig: Partial<
    Record<
      NormalizedMessage["status"],
      {
        label: string;
        icon: React.ReactNode;
        iconClassName: string;
        pulse?: boolean;
      }
    >
  > = {
    sending: {
      label: "Sending",
      icon: <RiTimeFill className="h-4 w-4" />,
      iconClassName: "text-amber-600 dark:text-amber-400 animate-spin",
      pulse: true,
    },
    sent: {
      label: "Sent",
      icon: <RiCheckFill className="h-4 w-4" />,
      iconClassName: "text-sky-600 dark:text-sky-400",
    },
    delivered: {
      label: "Delivered",
      icon: <RiCheckDoubleFill className="h-4 w-4" />,
      iconClassName: "text-indigo-600 dark:text-indigo-400",
    },
    read: {
      label: "Read",
      icon: <RiCheckDoubleFill className="h-4 w-4" />,
      iconClassName: "text-emerald-600 dark:text-emerald-400",
    },
    failed: {
      label: "Failed",
      icon: <RiCloseCircleFill className="h-4 w-4" />,
      iconClassName: "text-rose-600 dark:text-rose-400",
    },
  };

  const config = statusConfig[status];

  if (!config) {
    return null;
  }

  return (
    <span
      className={`inline-flex items-center justify-center ${config.iconClassName} ${config.pulse ? "animate-pulse" : ""}`}
      title={config.label}
    >
      {config.icon}
    </span>
  );
};

// Message type renderers
const TextMessage = ({ content }: { content: string }) => (
  <div className="break-words whitespace-pre-wrap">{content}</div>
);

const MediaMessage = ({ message }: { message: NormalizedMessage }) => {
  const getMediaIcon = () => {
    switch (message.type) {
      case "image":
        return "🖼️";
      case "audio":
        return "🎵";
      case "video":
        return "🎥";
      case "document":
        return "📄";
      default:
        return "📎";
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <span className="text-lg">{getMediaIcon()}</span>
      <div>
        <p className="font-medium">
          {message.type.charAt(0).toUpperCase() + message.type.slice(1)}
        </p>
        {message.content && (
          <p className="text-sm opacity-75">{message.content}</p>
        )}
      </div>
    </div>
  );
};

const LocationMessage = ({ content }: { content: string }) => (
  <div className="flex items-center space-x-2">
    <span className="text-lg">📍</span>
    <div>
      <p className="font-medium">Location</p>
      <p className="text-sm opacity-75">{content}</p>
    </div>
  </div>
);

const ContactMessage = ({ content }: { content: string }) => (
  <div className="flex items-center space-x-2">
    <span className="text-lg">👤</span>
    <div>
      <p className="font-medium">Contact</p>
      <p className="text-sm opacity-75">{content}</p>
    </div>
  </div>
);
const formatTime = (timestamp: string): string => {
  return dayjs(timestamp).format("HH:mm");
};


const MessageBubble: React.FC<MessageBubbleProps> = memo(
  ({ message, isFirstInGroup, isLastInGroup, activeChat }) => {
    const isOwnMessage = message.sender.type === "user";

    const renderMessageContent = () => {
      switch (message.type) {
        case "text":
          return <TextMessage content={message.content} />;
        case "image":
        case "audio":
        case "video":
        case "document":
          return <MediaMessage message={message} />;
        case "location":
          return <LocationMessage content={message.content} />;
        case "contact":
          return <ContactMessage content={message.content} />;
        default:
          return <TextMessage content={message.content} />;
      }
    };

    return (
      <div
        className={`flex ${isOwnMessage ? "justify-end" : "justify-start"} ${isLastInGroup ? "mb-4" : "mb-1"}`}
      >
        <div
          className={`flex flex-col ${isOwnMessage ? "items-end" : "items-start"} max-w-xs lg:max-w-md xl:max-w-lg`}
        >
          <div
            className={`flex items-end space-x-2 w-full ${isOwnMessage ? "flex-row-reverse space-x-reverse" : ""}`}
          >
            {/* Avatar (only for contact messages and first in group) */}
            {!isOwnMessage && isFirstInGroup && (
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center flex-shrink-0">
                <span className="text-white text-xs font-medium">
                  {getInitials(activeChat.name)}
                </span>
              </div>
            )}

            {!isOwnMessage && !isFirstInGroup && (
              <div className="w-8 h-8" /> // Spacer for alignment
            )}

            {/* Message Bubble */}
            <div
              className={`
            px-4 py-2 shadow-sm max-w-full
            ${
              isOwnMessage
                ? "bg-blue-500 text-white"
                : "bg-white dark:bg-gray-800 dark:text-white border border-gray-200 dark:border-gray-700"
            }
            ${
              isFirstInGroup && isLastInGroup
                ? "rounded-2xl"
                : isFirstInGroup
                  ? isOwnMessage
                    ? "rounded-2xl rounded-br-md"
                    : "rounded-2xl rounded-bl-md"
                  : isLastInGroup
                    ? isOwnMessage
                      ? "rounded-2xl rounded-tr-md"
                      : "rounded-2xl rounded-tl-md"
                    : isOwnMessage
                      ? "rounded-r-2xl rounded-l-md"
                      : "rounded-l-2xl rounded-r-md"
            }
          `}
            >
              {/* Reply indicator */}
              {message.replyTo && (
                <div
                  className={`
                text-xs mb-2 pb-2 border-l-2 pl-2
                ${
                  isOwnMessage
                    ? "border-blue-300 text-blue-100"
                    : "border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-300"
                }
              `}
                >
                  <p className="font-medium">{message.replyTo.sender}</p>
                  <p className="truncate">{message.replyTo.content}</p>
                </div>
              )}

              {/* Message content */}
              <div className="text-sm">{renderMessageContent()}</div>

              {/* Reactions */}
              {message.reactions && message.reactions.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {message.reactions.map((reaction, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 dark:bg-gray-700"
                    >
                      {reaction.emoji}
                      <span className="ml-1 text-gray-600 dark:text-gray-300">
                        1
                      </span>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Message metadata (timestamp and status) - Only show for last message in group */}
          {isLastInGroup && (
            <div
              className={`
              flex items-center mt-1 space-x-2 text-xs
              ${isOwnMessage ? "justify-end" : "justify-start pl-10"}
              ${isOwnMessage ? "text-blue-500 dark:text-blue-400" : "text-gray-500 dark:text-gray-400"}
            `}
            >
              {/* Forwarded indicator */}
              {message.metadata?.forwarded && (
                <span className="italic opacity-75">Forwarded</span>
              )}

              {/* Timestamp with improved positioning - only for last message in group */}
              <span className="opacity-75">{formatTime(message.timestamp)}</span>

              {/* Status (only for own messages) with better spacing */}
              {isOwnMessage && (
                <div className="flex items-center">
                  <MessageStatusIcon status={message.status} />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  },
);

MessageBubble.displayName = "MessageBubble";

export default MessageBubble;
