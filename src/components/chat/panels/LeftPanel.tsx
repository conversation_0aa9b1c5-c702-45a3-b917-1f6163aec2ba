import type { LeftPanelProps, Project } from "@/types";

import React, { useEffect, useMemo, useRef } from "react";
import { Input } from "@heroui/input";
import { Button } from "@heroui/button";
import { useDisclosure } from "@heroui/modal";
import { useOutletContext } from "react-router-dom";

import ChatListItem from "./ChatListItem";
import FilterDrawer from "./FilterDrawer";

import { useAppDispatch, useAppSelector } from "@/store/hooks.ts";
import { addChat, setError, setSearchQuery } from "@/store/chatSlice.ts";
import { setActiveChat } from "@/store/messageSlice.ts";
import { setMainMenuExpanded, showMobileChatView } from "@/store/uiSlice.ts";
import { ChatModel } from "@/types/firestore/chatModel.ts";
import subscribeToChats from "@/services/firebase/chatService.ts";

// Hamburger Menu Icon
const MenuIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M4 6h16M4 12h16M4 18h16"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

// Search Icon
const SearchIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

// Filter Icon
const FilterIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const LeftPanel: React.FC<LeftPanelProps> = ({ className }) => {
  const dispatch = useAppDispatch();
  const { chats, searchQuery, error } = useAppSelector((state) => state.chat);
  const { activeChat } = useAppSelector((state) => state.message);
  const { screenSize } = useAppSelector((state) => state.ui);
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const { project } = useOutletContext<{
    project: Project;
  }>();

  // Filter and search logic
  const filteredChats: ChatModel[] = useMemo(() => {
    if (!searchQuery) {
      return chats;
    }

    const query = searchQuery.toLowerCase();

    return chats.filter((chat) =>
      chat.phoneNumber.toLowerCase().includes(query),
    );
  }, [chats, searchQuery]);

  const handleChatSelect = (chat: ChatModel) => {
    dispatch(setActiveChat(chat));

    // On mobile, switch to chat view
    if (screenSize === "mobile") {
      dispatch(showMobileChatView());
    }
  };

  const handleOpenMainMenu = () => {
    dispatch(setMainMenuExpanded(true));
  };

  const handleSearchChange = (value: string) => {
    dispatch(setSearchQuery(value));
  };

  useEffect(() => {
    const unsubscribe = subscribeToChats(
      project.uid,
      (chats) => {
        chats.forEach((chat) => {
          dispatch(addChat(chat));
        });
      },
      () => {
        dispatch(setError("Failed to load chats"));
      },
    );

    return () => {
      unsubscribe();
    };
  }, [project.uid]);

  const containerRef = useRef<HTMLDivElement>(null);

  return (
    <div
      ref={containerRef}
      className={`relative flex flex-col h-full bg-white dark:bg-gray-800 shadow-md dark:shadow-black/25 border-r border-gray-200 dark:border-gray-600 ${className}`}
    >
      {/* Header */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            {/* Mobile Hamburger Menu Button */}
            {screenSize === "mobile" && (
              <Button
                isIconOnly
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                size="sm"
                variant="light"
                onPress={handleOpenMainMenu}
              >
                <MenuIcon />
              </Button>
            )}
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
              Chats
            </h1>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              isDisabled
              isIconOnly
              className="text-gray-300 dark:text-gray-600 cursor-not-allowed"
              size="sm"
              variant="light"
              onPress={onOpen}
            >
              <FilterIcon />
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Input
            className="w-full"
            classNames={{
              input: "text-sm",
              inputWrapper:
                "bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600",
            }}
            placeholder="Search Phone Number"
            startContent={<SearchIcon />}
            type="text"
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
          />
        </div>
        {error && (
          <div className="mt-2 text-sm text-red-500 dark:text-red-400">
            {error}
          </div>
        )}

        {/* Active Filters */}
        <div className="relative mt-4">
          <div className="flex flex-nowrap gap-2 overflow-x-auto scroll-smooth transparent-scrollbar">
            {/* TODO: Implement real filter data */}
            {/* {activeFilters.map((filter, index) => {
              const fullText = `${filter.type}: ${filter.value}`;
              const truncatedText =
                fullText.length > 25
                  ? fullText.substring(0, 22) + "..."
                  : fullText;

              return (
                <div
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 dark:bg-blue-900 text-blue-80 dark:text-blue-200 flex-shrink-0"
                >
                  <span title={fullText}>{truncatedText}</span>
                  <button
                    className="ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                    onClick={() =>
                      console.log(
                        `Removing filter: ${filter.type}: ${filter.value}`,
                      )
                    }
                  >
                    ×
                  </button>
                </div>
              );
            })} */}
          </div>
          {/* Fade gradients for scroll indicators */}
          <div className="absolute right-0 top-0 bottom-0 w-4 bg-gradient-to-l from-gray-50 dark:from-gray-700 to-transparent pointer-events-none" />
        </div>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {filteredChats.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-gray-500 dark:text-gray-400">
            <p className="text-sm">No phone numbers found</p>
            {searchQuery && (
              <Button
                className="mt-2"
                size="sm"
                variant="light"
                onPress={() => dispatch(setSearchQuery(""))}
              >
                Clear search
              </Button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-100 dark:divide-gray-700">
            {filteredChats.map((chat) => (
              <ChatListItem
                key={chat.id}
                chat={chat}
                isActive={activeChat ? chat.id === activeChat.id : false}
                onClick={() => handleChatSelect(chat)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Footer Stats */}
      <div className="p-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>{filteredChats.length} phone numbers</span>
        </div>
      </div>

      <FilterDrawer isOpen={isOpen} onOpenChange={onOpenChange} />
    </div>
  );
};

export default LeftPanel;
