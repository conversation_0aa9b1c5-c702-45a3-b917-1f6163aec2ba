import React, { useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import dayjs from "dayjs";

import MessageBubble from "./MessageBubble";

import { useAppDispatch, useAppSelector } from "@/store/hooks.ts";
import { setLoading, setMessages } from "@/store/messageSlice.ts";
import subscribeToMessages from "@/services/firebase/messageService.ts";

interface MessageAreaProps {
  chatId: string;
}

const MessageArea: React.FC<MessageAreaProps> = ({ chatId }) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const dispatch = useAppDispatch();
  const { projectId } = useParams();
  const { messages, isLoading, activeChat } = useAppSelector(
    (state) => state.message,
  );
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const messagesRef = useRef(messages);

  // Update ref when messages change
  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);

  const chatMessages = messages[chatId] || [];

  // Setup realtime message listener
  useEffect(() => {
    if (!projectId || !chatId) return;

    let unsubscribe: (() => void) | null = null;

    dispatch(setLoading(true));
    setConnectionError(null);

    const setupListener = () => {
      unsubscribe = subscribeToMessages(
        projectId,
        chatId,
        (newMessages) => {
          dispatch(
            setMessages({
              ...messagesRef.current,
              [chatId]: newMessages,
            }),
          );
          dispatch(setLoading(false));
        },
        (error) => {
          console.error("Realtime message error:", error);
          setConnectionError(
            "Failed to connect to messages. Please check your connection.",
          );
          dispatch(setLoading(false));
        },
      );
    };

    setupListener();

    // Cleanup listener on component unmount
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [projectId, chatId, dispatch]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [chatMessages]);

  // Define the type for grouped messages
  type GroupedMessagesType = Record<
    string,
    Record<string, typeof chatMessages>
  >;

  // Group messages by minute for the same sender
  const groupedMessages = chatMessages.reduce((groups, message) => {
    const dateKey = dayjs(message.timestamp).format("YYYY-MM-DD");
    const minuteKey = dayjs(message.timestamp).format("YYYY-MM-DD HH:mm");
    const groupKey = `${minuteKey}_${message.sender.id}`;

    if (!groups[dateKey]) {
      groups[dateKey] = {};
    }

    if (!groups[dateKey][groupKey]) {
      groups[dateKey][groupKey] = [];
    }
    groups[dateKey][groupKey].push(message);

    return groups;
  }, {} as GroupedMessagesType);

  const formatDateHeader = (dateString: string): string => {
    const date = dayjs(dateString);
    const today = dayjs();
    const yesterday = today.subtract(1, "day");

    if (date.isSame(today, "day")) {
      return "Today";
    } else if (date.isSame(yesterday, "day")) {
      return "Yesterday";
    } else {
      return date.format("MMMM D, YYYY");
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full bg-white dark:bg-gray-800">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg
              className="animate-spin h-8 w-8 text-gray-400 dark:text-gray-500"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                fill="currentColor"
              />
            </svg>
          </div>
          <p className="text-gray-500 dark:text-gray-400">
            Loading messages...
          </p>
        </div>
      </div>
    );
  }

  if (connectionError) {
    return (
      <div className="flex items-center justify-center h-full bg-white dark:bg-gray-800">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-red-500 dark:text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
              />
            </svg>
          </div>
          <p className="text-red-500 dark:text-red-400 mb-2">
            {connectionError}
          </p>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!activeChat) {
    return (
      <div className="flex items-center justify-center h-full bg-white dark:bg-gray-800">
        <div className="text-center">
          <p className="text-gray-500 dark:text-gray-400">No chat selected</p>
        </div>
      </div>
    );
  }

  if (chatMessages.length === 0) {
    return (
      <div className="flex items-center justify-center h-full bg-white dark:bg-gray-800">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-gray-400 dark:text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
              />
            </svg>
          </div>
          <p className="text-gray-500 dark:text-gray-400">
            No messages yet. Start the conversation!
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={scrollRef}
      className="h-full overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900 shadow-inner dark:shadow-black/30"
    >
      {Object.entries(groupedMessages).map(([dateKey, groupsForDate]) => (
        <div key={dateKey}>
          {/* Date Separator */}
          <div className="flex items-center justify-center mb-4">
            <div className="bg-white dark:bg-gray-800 px-3 py-1 rounded-full shadow-sm border border-gray-200 dark:border-gray-700">
              <span className="text-xs font-medium text-gray-500 dark:text-gray-300">
                {formatDateHeader(dateKey)}
              </span>
            </div>
          </div>

          {/* Messages for this date */}
          <div className="space-y-2">
            {Object.values(groupsForDate).map((messageGroup) => {
              return messageGroup.map((message, index) => {
                const isFirstInGroup = index === 0;
                const isLastInGroup = index === messageGroup.length - 1;

                return (
                  <MessageBubble
                    key={message.id}
                    activeChat={activeChat}
                    isFirstInGroup={isFirstInGroup}
                    isLastInGroup={isLastInGroup}
                    message={message}
                  />
                );
              });
            })}
          </div>
        </div>
      ))}

      {/* Typing indicator placeholder */}
      {/* You can add a typing indicator component here */}
    </div>
  );
};

export default MessageArea;
