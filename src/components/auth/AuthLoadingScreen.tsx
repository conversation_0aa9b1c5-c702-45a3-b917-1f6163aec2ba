const AuthLoadingScreen = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 via-slate-50 to-white dark:from-slate-950 dark:via-slate-900 dark:to-slate-900 flex items-center justify-center px-6 py-12">
      <div className="flex flex-col items-center gap-6 rounded-3xl bg-white/80 p-8 shadow-2xl shadow-slate-900/10 backdrop-blur-sm dark:bg-slate-900/70 dark:shadow-black/30">
        <div className="relative h-16 w-16">
          <span className="absolute inset-0 rounded-full border-4 border-slate-200 dark:border-slate-800" />
          <span className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-500 border-r-blue-500 animate-spin" />
        </div>

        <div className="text-center space-y-2">
          <p className="text-xl font-semibold text-slate-900 dark:text-slate-100">
            Mengonfirmasi akses Anda
          </p>
          <p className="text-sm text-slate-500 dark:text-slate-400">
            Memastikan status autentikasi sebelum kami membawa Anda ke dalam
            aplikasi.
          </p>
        </div>

        <div className="flex items-center gap-2 text-xs font-medium uppercase tracking-wide text-slate-400 dark:text-slate-500">
          <span className="h-1.5 w-1.5 rounded-full bg-blue-500 animate-pulse" />
          <span>Mengecek token sesi...</span>
        </div>
      </div>
    </div>
  );
};

export default AuthLoadingScreen;
